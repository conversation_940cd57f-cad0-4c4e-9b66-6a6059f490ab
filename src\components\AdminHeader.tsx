'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Search,
  Home,
  Package,
  Users,
  Image,
  Moon,
  Sun,
  LogOut,
  User,
  Filter,
  Clock,
  X,
  Command,
  ArrowRight,
  Star,
  History,
  Zap,
  Bell,
  AlertTriangle,
  CheckCircle,
  Info,
  Volume2,
  VolumeX
} from 'lucide-react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { useAuth } from '@/contexts/AuthContext'

interface AdminHeaderProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [isSearchLoading, setIsSearchLoading] = useState(false)
  const [showSearchDropdown, setShowSearchDropdown] = useState(false)
  const { setTheme, resolvedTheme } = useTheme()
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { user, logout } = useAuth()
  const searchRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const notificationRef = useRef<HTMLDivElement>(null)
  const quickActionsRef = useRef<HTMLDivElement>(null)

  // Handle hydration
  useEffect(() => {
    setMounted(true)
    // Load search history from localStorage
    const savedHistory = localStorage.getItem('searchHistory')
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    }

    // Load notification preferences
    const soundPref = localStorage.getItem('notificationSound')
    if (soundPref !== null) {
      setSoundEnabled(JSON.parse(soundPref))
    }

    // Initialize notifications
    fetchNotifications()
  }, [])

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      // Simulate fetching notifications
      const mockNotifications = [
        {
          id: 1,
          type: 'warning',
          title: 'Low Stock Alert',
          message: '5 products are running low on stock',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          read: false,
          icon: AlertTriangle,
          color: 'orange'
        },
        {
          id: 2,
          type: 'info',
          title: 'New Debt Record',
          message: 'Customer Juan added a new debt of ₱150.00',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          read: false,
          icon: Users,
          color: 'blue'
        },
        {
          id: 3,
          type: 'success',
          title: 'Payment Received',
          message: 'Maria paid ₱200.00 towards her debt',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          read: true,
          icon: CheckCircle,
          color: 'green'
        }
      ]

      setNotifications(mockNotifications)
      setUnreadCount(mockNotifications.filter(n => !n.read).length)
    } catch (error) {
      console.error('Error fetching notifications:', error)
    }
  }

  // Play notification sound
  const playNotificationSound = () => {
    if (soundEnabled && mounted) {
      // Create a simple notification sound
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    }
  }

  // Mark notification as read
  const markAsRead = (notificationId: number) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    )
    setUnreadCount(prev => Math.max(0, prev - 1))
  }

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
    setUnreadCount(0)
  }

  // Toggle sound
  const toggleSound = () => {
    const newSoundEnabled = !soundEnabled
    setSoundEnabled(newSoundEnabled)
    localStorage.setItem('notificationSound', JSON.stringify(newSoundEnabled))
  }

  // Handle click outside dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchDropdown(false)
        setIsSearchFocused(false)
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setIsNotificationsOpen(false)
      }
      if (quickActionsRef.current && !quickActionsRef.current.contains(event.target as Node)) {
        setIsQuickActionsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add new notifications (simulate real-time updates)
      if (Math.random() < 0.1) { // 10% chance every 30 seconds
        const newNotification = {
          id: Date.now(),
          type: 'info',
          title: 'System Update',
          message: 'Dashboard data has been refreshed',
          timestamp: new Date(),
          read: false,
          icon: Info,
          color: 'blue'
        }

        setNotifications(prev => [newNotification, ...prev.slice(0, 9)]) // Keep only 10 notifications
        setUnreadCount(prev => prev + 1)
        playNotificationSound()
      }
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [soundEnabled, mounted])

  // Advanced search functionality
  useEffect(() => {
    const performSearch = async () => {
      if (searchQuery.length < 2) {
        setSearchResults([])
        return
      }

      setIsSearchLoading(true)
      try {
        // Simulate API calls for search
        const [productsRes, debtsRes] = await Promise.all([
          fetch(`/api/products?search=${encodeURIComponent(searchQuery)}`),
          fetch(`/api/debts?search=${encodeURIComponent(searchQuery)}`)
        ])

        const productsData = await productsRes.json()
        const debtsData = await debtsRes.json()

        const results = [
          ...((productsData.products || []).slice(0, 3).map((item: any) => ({
            ...item,
            type: 'product',
            icon: Package
          }))),
          ...((debtsData.debts || []).slice(0, 3).map((item: any) => ({
            ...item,
            type: 'debt',
            icon: Users
          })))
        ]

        setSearchResults(results)
      } catch (error) {
        console.error('Search error:', error)
        setSearchResults([])
      } finally {
        setIsSearchLoading(false)
      }
    }

    const debounceTimer = setTimeout(performSearch, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchQuery])



  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Home Dashboard',
      icon: Home,
      tooltip: 'Dashboard Overview'
    },
    {
      id: 'products',
      label: 'Product Lists',
      icon: Package,
      tooltip: 'Manage Products'
    },
    {
      id: 'debts',
      label: 'Customer Debts',
      icon: Users,
      tooltip: 'Customer Debt Management'
    },
    {
      id: 'family-gallery',
      label: 'Family Gallery',
      icon: Image,
      tooltip: 'Family Photos & Memories'
    },
  ]

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Add to search history
      const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 5)
      setSearchHistory(newHistory)
      localStorage.setItem('searchHistory', JSON.stringify(newHistory))

      // Perform search action
      console.log('Searching for:', searchQuery)
      setShowSearchDropdown(false)
    }
  }

  const handleSearchFocus = () => {
    setIsSearchFocused(true)
    setShowSearchDropdown(true)
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchQuery(value)
    if (value.length > 0) {
      setShowSearchDropdown(true)
    }
  }

  const selectSearchResult = (result: any) => {
    setSearchQuery('')
    setShowSearchDropdown(false)
    // Navigate to appropriate section based on result type
    if (result.type === 'product') {
      setActiveSection('products')
    } else if (result.type === 'debt') {
      setActiveSection('debts')
    }
  }

  const selectSearchHistory = (historyItem: string) => {
    setSearchQuery(historyItem)
    setShowSearchDropdown(false)
    searchInputRef.current?.focus()
  }

  const clearSearchHistory = () => {
    setSearchHistory([])
    localStorage.removeItem('searchHistory')
  }

  // Enhanced Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        searchInputRef.current?.focus()
        setShowSearchDropdown(true)
      }

      // Escape to close dropdowns
      if (e.key === 'Escape') {
        setShowSearchDropdown(false)
        setIsSearchFocused(false)
        setIsNotificationsOpen(false)
        setIsProfileOpen(false)
        setIsQuickActionsOpen(false)
        searchInputRef.current?.blur()
      }

      // Quick navigation shortcuts (Alt + number)
      if (e.altKey && !e.ctrlKey && !e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault()
            setActiveSection('dashboard')
            break
          case '2':
            e.preventDefault()
            setActiveSection('products')
            break
          case '3':
            e.preventDefault()
            setActiveSection('debts')
            break
          case '4':
            e.preventDefault()
            setActiveSection('family-gallery')
            break
        }
      }

      // Quick action shortcuts (Ctrl/Cmd + Shift + letter)
      if ((e.ctrlKey || e.metaKey) && e.shiftKey) {
        switch (e.key.toLowerCase()) {
          case 'p':
            e.preventDefault()
            // Quick add product action
            console.log('Quick add product shortcut')
            setActiveSection('products')
            break
          case 'd':
            e.preventDefault()
            // Quick add debt action
            console.log('Quick add debt shortcut')
            setActiveSection('debts')
            break
          case 'n':
            e.preventDefault()
            // Toggle notifications
            setIsNotificationsOpen(!isNotificationsOpen)
            break
          case 't':
            e.preventDefault()
            // Toggle theme
            toggleTheme()
            break
          case 'a':
            e.preventDefault()
            // Toggle quick actions menu
            setIsQuickActionsOpen(!isQuickActionsOpen)
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isNotificationsOpen, isQuickActionsOpen, resolvedTheme, mounted])

  const toggleTheme = () => {
    if (!mounted) return

    // Manual DOM manipulation for immediate visual feedback
    const html = document.documentElement
    const isDark = resolvedTheme === 'dark'

    if (isDark) {
      html.classList.remove('dark')
      setTheme('light')
    } else {
      html.classList.add('dark')
      setTheme('dark')
    }
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300" style={{
      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
    }}>
      <div className="grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4">
        
        {/* Left Section - Logo & Search (Fixed Width) */}
        <div className="flex items-center space-x-3 w-auto">
          {/* Revantad Logo */}
          <Link
            href="/landing"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0"
            title="Return to Front Page"
          >
            <div className="w-10 h-10 hero-gradient rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-lg">R</span>
            </div>
            <span className="text-xl font-bold text-gradient hidden sm:block">Revantad</span>
          </Link>

          {/* Enhanced Search Bar */}
          <div ref={searchRef} className="relative w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-200 ${
                  isSearchFocused ? 'text-green-500' : 'text-gray-400'
                }`} />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search... (Ctrl+K)"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onFocus={handleSearchFocus}
                  className="w-full pl-10 pr-10 py-2 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                  style={{
                    backgroundColor: isSearchFocused
                      ? (resolvedTheme === 'dark' ? '#475569' : '#ffffff')
                      : (resolvedTheme === 'dark' ? '#334155' : '#f3f4f6'),
                    border: 'none',
                    outline: 'none',
                    boxShadow: 'none',
                    WebkitAppearance: 'none',
                    MozAppearance: 'none',
                    appearance: 'none'
                  }}
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchQuery('')
                      setSearchResults([])
                      setShowSearchDropdown(false)
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
                {!searchQuery && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                    <Command className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-400">K</span>
                  </div>
                )}
              </div>
            </form>

            {/* Advanced Search Dropdown */}
            {showSearchDropdown && (
              <div
                className="absolute top-full mt-2 w-80 rounded-xl shadow-2xl border z-50 max-h-96 overflow-y-auto"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
                }}
              >
                {/* Search Results */}
                {searchQuery.length >= 2 && (
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4
                        className="text-sm font-semibold transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                        }}
                      >
                        Search Results
                      </h4>
                      {isSearchLoading && (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-green-500 border-t-transparent"></div>
                      )}
                    </div>

                    {searchResults.length > 0 ? (
                      <div className="space-y-2">
                        {searchResults.map((result, index) => {
                          const Icon = result.icon
                          return (
                            <button
                              key={index}
                              onClick={() => selectSearchResult(result)}
                              className="w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                              style={{
                                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
                              }}
                            >
                              <div className={`p-2 rounded-full ${
                                result.type === 'product' ? 'bg-blue-100 dark:bg-blue-900' : 'bg-green-100 dark:bg-green-900'
                              }`}>
                                <Icon className={`h-4 w-4 ${
                                  result.type === 'product' ? 'text-blue-600' : 'text-green-600'
                                }`} />
                              </div>
                              <div className="flex-1 text-left">
                                <p
                                  className="font-medium text-sm transition-colors duration-300"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                                  }}
                                >
                                  {result.name || result.customer_name}
                                </p>
                                <p
                                  className="text-xs transition-colors duration-300"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                                  }}
                                >
                                  {result.type === 'product' ? 'Product' : 'Customer Debt'}
                                </p>
                              </div>
                              <ArrowRight className="h-4 w-4 text-gray-400" />
                            </button>
                          )
                        })}
                      </div>
                    ) : !isSearchLoading && (
                      <div className="text-center py-4">
                        <p
                          className="text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          No results found
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Search History */}
                {searchQuery.length === 0 && searchHistory.length > 0 && (
                  <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                      <h4
                        className="text-sm font-semibold transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                        }}
                      >
                        Recent Searches
                      </h4>
                      <button
                        onClick={clearSearchHistory}
                        className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200"
                      >
                        Clear
                      </button>
                    </div>
                    <div className="space-y-1">
                      {searchHistory.map((item, index) => (
                        <button
                          key={index}
                          onClick={() => selectSearchHistory(item)}
                          className="w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                          }}
                        >
                          <History className="h-4 w-4 text-gray-400" />
                          <span
                            className="text-sm transition-colors duration-300"
                            style={{
                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                            }}
                          >
                            {item}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                {searchQuery.length === 0 && (
                  <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                    <h4
                      className="text-sm font-semibold mb-3 transition-colors duration-300"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                      }}
                    >
                      Quick Actions
                    </h4>
                    <div className="space-y-1">
                      <button
                        onClick={() => {
                          setActiveSection('products')
                          setShowSearchDropdown(false)
                        }}
                        className="w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                        style={{
                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                        }}
                      >
                        <Package className="h-4 w-4 text-blue-500" />
                        <span
                          className="text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          View Products
                        </span>
                      </button>
                      <button
                        onClick={() => {
                          setActiveSection('debts')
                          setShowSearchDropdown(false)
                        }}
                        className="w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                        style={{
                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                        }}
                      >
                        <Users className="h-4 w-4 text-green-500" />
                        <span
                          className="text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          View Debts
                        </span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Center Section - Navigation Icons (Facebook-style) */}
        <div className="hidden sm:flex items-center justify-center">
          <div className="flex items-center space-x-3 md:space-x-4 lg:space-x-5">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = activeSection === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]`}
                  style={{
                    backgroundColor: isActive
                      ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                      : 'transparent',
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),
                    boxShadow: isActive ? '0 2px 8px rgba(34, 197, 94, 0.2)' : 'none'
                  }}
                  title={item.tooltip}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(243, 244, 246, 0.8)'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent'
                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'
                    }
                  }}
                >
                  <Icon className="h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200" />
                  {isActive && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full"></div>
                  )}

                  {/* Enhanced Tooltip */}
                  <div className="absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10">
                    {item.tooltip}
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45"></div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Mobile Navigation - Simplified */}
        <div className="sm:hidden flex items-center justify-center">
          <button
            onClick={() => setActiveSection(activeSection === 'dashboard' ? 'products' : 'dashboard')}
            className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
            title="Toggle View"
          >
            {activeSection === 'dashboard' ? (
              <Package className="h-5 w-5" />
            ) : (
              <Home className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* Right Section - Quick Actions, Notifications, Dark Mode & Profile */}
        <div className="flex items-center justify-end space-x-3">

          {/* Quick Actions */}
          <div ref={quickActionsRef} className="relative flex-shrink-0">
            <button
              onClick={() => setIsQuickActionsOpen(!isQuickActionsOpen)}
              className="p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 group"
              title="Quick Actions (Ctrl+Shift+A)"
            >
              <Zap className="h-5 w-5" />
            </button>

            {/* Quick Actions Dropdown */}
            {isQuickActionsOpen && (
              <div
                className="absolute right-0 mt-2 w-72 rounded-xl shadow-2xl border z-50"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
                }}
              >
                {/* Quick Actions Header */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h3
                      className="text-lg font-semibold transition-colors duration-300"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                      }}
                    >
                      Quick Actions
                    </h3>
                    <Zap className="h-5 w-5 text-yellow-500" />
                  </div>
                  <p
                    className="text-sm mt-1 transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}
                  >
                    Keyboard shortcuts for common tasks
                  </p>
                </div>

                {/* Quick Actions List */}
                <div className="p-2 space-y-1">
                  <button
                    onClick={() => {
                      setActiveSection('products')
                      setIsQuickActionsOpen(false)
                    }}
                    className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                        <Package className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="text-left">
                        <p
                          className="font-medium text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                          }}
                        >
                          Add Product
                        </p>
                        <p
                          className="text-xs transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          Add new inventory item
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-400">
                      <span>Ctrl+Shift+P</span>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      setActiveSection('debts')
                      setIsQuickActionsOpen(false)
                    }}
                    className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-green-100 dark:bg-green-900">
                        <Users className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="text-left">
                        <p
                          className="font-medium text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                          }}
                        >
                          Record Debt
                        </p>
                        <p
                          className="text-xs transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          Add customer debt record
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-400">
                      <span>Ctrl+Shift+D</span>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      setIsNotificationsOpen(true)
                      setIsQuickActionsOpen(false)
                    }}
                    className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900">
                        <Bell className="h-4 w-4 text-purple-600" />
                      </div>
                      <div className="text-left">
                        <p
                          className="font-medium text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                          }}
                        >
                          Notifications
                        </p>
                        <p
                          className="text-xs transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          View notifications
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-400">
                      <span>Ctrl+Shift+N</span>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      toggleTheme()
                      setIsQuickActionsOpen(false)
                    }}
                    className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900">
                        {resolvedTheme === 'dark' ? (
                          <Sun className="h-4 w-4 text-yellow-600" />
                        ) : (
                          <Moon className="h-4 w-4 text-yellow-600" />
                        )}
                      </div>
                      <div className="text-left">
                        <p
                          className="font-medium text-sm transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                          }}
                        >
                          Toggle Theme
                        </p>
                        <p
                          className="text-xs transition-colors duration-300"
                          style={{
                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                          }}
                        >
                          Switch to {resolvedTheme === 'dark' ? 'light' : 'dark'} mode
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-400">
                      <span>Ctrl+Shift+T</span>
                    </div>
                  </button>
                </div>

                {/* Navigation Shortcuts */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <h4
                    className="text-sm font-semibold mb-3 transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    Navigation Shortcuts
                  </h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex justify-between">
                      <span
                        className="transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Dashboard
                      </span>
                      <span className="text-gray-400">Alt+1</span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className="transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Products
                      </span>
                      <span className="text-gray-400">Alt+2</span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className="transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Debts
                      </span>
                      <span className="text-gray-400">Alt+3</span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className="transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        Gallery
                      </span>
                      <span className="text-gray-400">Alt+4</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Notifications */}
          <div ref={notificationRef} className="relative flex-shrink-0">
            <button
              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              className="relative p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 group"
              title="Notifications"
            >
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                    <span className="text-xs font-bold text-white">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  </div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full animate-ping opacity-75"></div>
                </>
              )}
            </button>

            {/* Notifications Dropdown */}
            {isNotificationsOpen && (
              <div
                className="absolute right-0 mt-2 w-80 rounded-xl shadow-2xl border z-50 max-h-96 overflow-hidden"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
                }}
              >
                {/* Notifications Header */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h3
                      className="text-lg font-semibold transition-colors duration-300"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                      }}
                    >
                      Notifications
                    </h3>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={toggleSound}
                        className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200"
                        title={soundEnabled ? 'Disable sound' : 'Enable sound'}
                      >
                        {soundEnabled ? (
                          <Volume2 className="h-4 w-4 text-green-500" />
                        ) : (
                          <VolumeX className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                      {unreadCount > 0 && (
                        <button
                          onClick={markAllAsRead}
                          className="text-xs text-blue-500 hover:text-blue-700 transition-colors duration-200"
                        >
                          Mark all read
                        </button>
                      )}
                    </div>
                  </div>
                  {unreadCount > 0 && (
                    <p
                      className="text-sm mt-1 transition-colors duration-300"
                      style={{
                        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                      }}
                    >
                      {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                    </p>
                  )}
                </div>

                {/* Notifications List */}
                <div className="max-h-80 overflow-y-auto">
                  {notifications.length > 0 ? (
                    <div className="space-y-1 p-2">
                      {notifications.map((notification) => {
                        const Icon = notification.icon
                        return (
                          <button
                            key={notification.id}
                            onClick={() => markAsRead(notification.id)}
                            className={`w-full p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] text-left ${
                              !notification.read ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''
                            }`}
                            style={{
                              backgroundColor: notification.read
                                ? (resolvedTheme === 'dark' ? '#334155' : '#f8fafc')
                                : undefined
                            }}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full flex-shrink-0 ${
                                notification.color === 'orange' ? 'bg-orange-100 dark:bg-orange-900' :
                                notification.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900' :
                                notification.color === 'green' ? 'bg-green-100 dark:bg-green-900' :
                                'bg-gray-100 dark:bg-gray-900'
                              }`}>
                                <Icon className={`h-4 w-4 ${
                                  notification.color === 'orange' ? 'text-orange-600' :
                                  notification.color === 'blue' ? 'text-blue-600' :
                                  notification.color === 'green' ? 'text-green-600' :
                                  'text-gray-600'
                                }`} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <p
                                    className="font-medium text-sm transition-colors duration-300"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                                    }}
                                  >
                                    {notification.title}
                                  </p>
                                  {!notification.read && (
                                    <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                  )}
                                </div>
                                <p
                                  className="text-xs mt-1 transition-colors duration-300"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                                  }}
                                >
                                  {notification.message}
                                </p>
                                <p
                                  className="text-xs mt-1 transition-colors duration-300"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'
                                  }}
                                >
                                  {notification.timestamp.toLocaleTimeString()}
                                </p>
                              </div>
                            </div>
                          </button>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <Bell className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p
                        className="text-sm transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                        }}
                      >
                        No notifications yet
                      </p>
                    </div>
                  )}
                </div>

                {/* Notifications Footer */}
                {notifications.length > 0 && (
                  <div className="p-3 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={() => {
                        setIsNotificationsOpen(false)
                        // Navigate to notifications page or history
                      }}
                      className="w-full text-center py-2 text-sm text-blue-500 hover:text-blue-700 transition-colors duration-200"
                    >
                      View all notifications
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>


          {/* Dark Mode Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0"
            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}
            disabled={!mounted}
          >
            {!mounted ? (
              <div className="h-5 w-5 bg-gray-400 rounded-full animate-pulse" />
            ) : resolvedTheme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </button>

          {/* Profile Dropdown */}
          <div className="relative flex-shrink-0">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                <User className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                {user?.name || 'Admin'}
              </span>
            </button>

            {/* Dropdown Menu */}
            {isProfileOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1">
                <div className="px-4 py-2 border-b border-gray-200 dark:border-slate-700">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email || '<EMAIL>'}</p>
                </div>
                
                <button
                  onClick={() => setActiveSection('settings')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2"
                >
                  <User className="h-4 w-4" />
                  <span>Settings</span>
                </button>
                
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
