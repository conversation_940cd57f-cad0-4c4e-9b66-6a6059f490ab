{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/FamilyGallery.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useRef } from 'react'\nimport {\n  Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon,\n  Search, Filter, Grid, List, Calendar, Eye, Star, Users, Camera,\n  Play, Pause, SkipBack, SkipForward, Maximize2, X, ChevronLeft, ChevronRight,\n  FolderOpen, Tag, Clock, TrendingUp, BarChart3, Activity, Zap,\n  Settings, MoreVertical, Edit, Copy, Archive, RefreshCw, SortAsc, SortDesc\n} from 'lucide-react'\n\ninterface Photo {\n  id: string\n  url: string\n  title: string\n  description: string\n  date: string\n  likes: number\n  isLiked: boolean\n  category: 'family' | 'store' | 'events' | 'products' | 'customers' | 'celebrations'\n  tags: string[]\n  views: number\n  isPrivate: boolean\n  uploadedBy: string\n  fileSize: string\n  dimensions: string\n  location?: string\n}\n\nexport default function FamilyGallery() {\n  const [photos, setPhotos] = useState<Photo[]>([\n    {\n      id: '1',\n      url: '/api/placeholder/400/300',\n      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa <PERSON>',\n      description: 'Grand opening sa aming Revantad Store kauban ang buong pamilya',\n      date: '2024-01-15',\n      likes: 12,\n      isLiked: true,\n      category: 'store',\n      tags: ['opening', 'family', 'milestone'],\n      views: 45,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '2.3 MB',\n      dimensions: '1920x1080',\n      location: 'Cebu City'\n    },\n    {\n      id: '2',\n      url: '/api/placeholder/400/300',\n      title: 'Anniversary Celebration',\n      description: 'Nag-celebrate mi sa first year sa business',\n      date: '2024-02-20',\n      likes: 8,\n      isLiked: false,\n      category: 'celebrations',\n      tags: ['anniversary', 'celebration', 'milestone'],\n      views: 32,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '1.8 MB',\n      dimensions: '1920x1080',\n      location: 'Cebu City'\n    },\n    {\n      id: '3',\n      url: '/api/placeholder/400/300',\n      title: 'Community Festival',\n      description: 'Nag-participate mi sa local community festival',\n      date: '2024-03-10',\n      likes: 15,\n      isLiked: true,\n      category: 'events',\n      tags: ['community', 'festival', 'participation'],\n      views: 67,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '3.1 MB',\n      dimensions: '1920x1080',\n      location: 'Barangay San Jose'\n    },\n    {\n      id: '4',\n      url: '/api/placeholder/400/300',\n      title: 'Mga Produkto sa Store',\n      description: 'Showcase sa mga bag-ong produkto sa aming tindahan',\n      date: '2024-03-25',\n      likes: 6,\n      isLiked: false,\n      category: 'products',\n      tags: ['products', 'showcase', 'inventory'],\n      views: 28,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '1.5 MB',\n      dimensions: '1920x1080'\n    },\n    {\n      id: '5',\n      url: '/api/placeholder/400/300',\n      title: 'Family Bonding Time',\n      description: 'Quality time sa pamilya after work sa tindahan',\n      date: '2024-04-05',\n      likes: 20,\n      isLiked: true,\n      category: 'family',\n      tags: ['family', 'bonding', 'quality-time'],\n      views: 89,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '2.7 MB',\n      dimensions: '1920x1080'\n    },\n    {\n      id: '6',\n      url: '/api/placeholder/400/300',\n      title: 'Mga Suki sa Tindahan',\n      description: 'Mga loyal customers nga nag-visit sa store',\n      date: '2024-04-12',\n      likes: 11,\n      isLiked: false,\n      category: 'customers',\n      tags: ['customers', 'loyalty', 'community'],\n      views: 41,\n      isPrivate: false,\n      uploadedBy: 'Admin',\n      fileSize: '2.1 MB',\n      dimensions: '1920x1080'\n    }\n  ])\n\n  // Enhanced state management\n  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)\n  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)\n  const [isLightboxOpen, setIsLightboxOpen] = useState(false)\n  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0)\n  const [isSlideshow, setIsSlideshow] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [sortBy, setSortBy] = useState<'date' | 'likes' | 'views' | 'title'>('date')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([])\n  const [showFilters, setShowFilters] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const [uploadForm, setUploadForm] = useState({\n    title: '',\n    description: '',\n    file: null as File | null,\n    category: 'family' as Photo['category'],\n    tags: '',\n    isPrivate: false,\n    location: ''\n  })\n\n  // Enhanced filtering and sorting logic\n  const filteredAndSortedPhotos = useMemo(() => {\n    let filtered = photos.filter(photo => {\n      const matchesSearch = searchTerm === '' ||\n        photo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n\n      const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory\n\n      return matchesSearch && matchesCategory\n    })\n\n    filtered.sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case 'date':\n          comparison = new Date(a.date).getTime() - new Date(b.date).getTime()\n          break\n        case 'likes':\n          comparison = a.likes - b.likes\n          break\n        case 'views':\n          comparison = a.views - b.views\n          break\n        case 'title':\n          comparison = a.title.localeCompare(b.title)\n          break\n      }\n      return sortOrder === 'desc' ? -comparison : comparison\n    })\n\n    return filtered\n  }, [photos, searchTerm, selectedCategory, sortBy, sortOrder])\n\n  // Enhanced photo interactions\n  const handleLike = (photoId: string) => {\n    setPhotos(photos.map(photo =>\n      photo.id === photoId\n        ? {\n            ...photo,\n            likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1,\n            isLiked: !photo.isLiked\n          }\n        : photo\n    ))\n  }\n\n  const handleView = (photoId: string) => {\n    setPhotos(photos.map(photo =>\n      photo.id === photoId\n        ? { ...photo, views: photo.views + 1 }\n        : photo\n    ))\n  }\n\n  const handleDelete = (photoId: string) => {\n    if (confirm('Sigurado ka ba nga i-delete ni nga photo?')) {\n      setPhotos(photos.filter(photo => photo.id !== photoId))\n      setSelectedPhotos(selectedPhotos.filter(id => id !== photoId))\n    }\n  }\n\n  const handleBulkDelete = () => {\n    if (selectedPhotos.length > 0 && confirm(`Sigurado ka ba nga i-delete ang ${selectedPhotos.length} ka photos?`)) {\n      setPhotos(photos.filter(photo => !selectedPhotos.includes(photo.id)))\n      setSelectedPhotos([])\n    }\n  }\n\n  const handleSelectPhoto = (photoId: string) => {\n    setSelectedPhotos(prev =>\n      prev.includes(photoId)\n        ? prev.filter(id => id !== photoId)\n        : [...prev, photoId]\n    )\n  }\n\n  const handleSelectAll = () => {\n    if (selectedPhotos.length === filteredAndSortedPhotos.length) {\n      setSelectedPhotos([])\n    } else {\n      setSelectedPhotos(filteredAndSortedPhotos.map(photo => photo.id))\n    }\n  }\n\n  // Enhanced lightbox functionality\n  const openLightbox = (photoIndex: number) => {\n    setCurrentPhotoIndex(photoIndex)\n    setIsLightboxOpen(true)\n    handleView(filteredAndSortedPhotos[photoIndex].id)\n  }\n\n  const closeLightbox = () => {\n    setIsLightboxOpen(false)\n    setIsSlideshow(false)\n  }\n\n  const nextPhoto = () => {\n    const nextIndex = (currentPhotoIndex + 1) % filteredAndSortedPhotos.length\n    setCurrentPhotoIndex(nextIndex)\n    handleView(filteredAndSortedPhotos[nextIndex].id)\n  }\n\n  const prevPhoto = () => {\n    const prevIndex = currentPhotoIndex === 0 ? filteredAndSortedPhotos.length - 1 : currentPhotoIndex - 1\n    setCurrentPhotoIndex(prevIndex)\n    handleView(filteredAndSortedPhotos[prevIndex].id)\n  }\n\n  const toggleSlideshow = () => {\n    setIsSlideshow(!isSlideshow)\n  }\n\n  // Enhanced upload functionality\n  const handleUpload = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (uploadForm.file && uploadForm.title) {\n      setIsLoading(true)\n\n      // Simulate upload delay\n      setTimeout(() => {\n        const newPhoto: Photo = {\n          id: Date.now().toString(),\n          url: URL.createObjectURL(uploadForm.file!),\n          title: uploadForm.title,\n          description: uploadForm.description,\n          date: new Date().toISOString().split('T')[0] || '',\n          likes: 0,\n          isLiked: false,\n          category: uploadForm.category,\n          tags: uploadForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag),\n          views: 0,\n          isPrivate: uploadForm.isPrivate,\n          uploadedBy: 'Admin',\n          fileSize: `${(uploadForm.file!.size / (1024 * 1024)).toFixed(1)} MB`,\n          dimensions: '1920x1080', // Would be calculated from actual image\n          location: uploadForm.location\n        }\n\n        setPhotos([newPhoto, ...photos])\n        setUploadForm({\n          title: '',\n          description: '',\n          file: null,\n          category: 'family',\n          tags: '',\n          isPrivate: false,\n          location: ''\n        })\n        setIsUploadModalOpen(false)\n        setIsLoading(false)\n      }, 1500)\n    }\n  }\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setUploadForm({ ...uploadForm, file })\n    }\n  }\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    const file = e.dataTransfer.files[0]\n    if (file && file.type.startsWith('image/')) {\n      setUploadForm({ ...uploadForm, file })\n    }\n  }\n\n  // Gallery statistics\n  const galleryStats = useMemo(() => {\n    const totalViews = photos.reduce((sum, photo) => sum + photo.views, 0)\n    const totalLikes = photos.reduce((sum, photo) => sum + photo.likes, 0)\n    const categoryCounts = photos.reduce((acc, photo) => {\n      acc[photo.category] = (acc[photo.category] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    return {\n      totalPhotos: photos.length,\n      totalViews,\n      totalLikes,\n      categoryCounts,\n      averageViews: Math.round(totalViews / photos.length) || 0,\n      mostPopular: photos.reduce((prev, current) => (prev.views > current.views) ? prev : current, photos[0])\n    }\n  }, [photos])\n\n  const categories = [\n    { id: 'all', label: 'Tanan', icon: Grid },\n    { id: 'family', label: 'Pamilya', icon: Users },\n    { id: 'store', label: 'Tindahan', icon: FolderOpen },\n    { id: 'events', label: 'Mga Event', icon: Calendar },\n    { id: 'products', label: 'Produkto', icon: Tag },\n    { id: 'customers', label: 'Mga Suki', icon: Heart },\n    { id: 'celebrations', label: 'Selebrasyon', icon: Star }\n  ]\n\n  return (\n    <div className=\"space-y-6 animate-fade-in\">\n      {/* Enhanced Header with Professional Design */}\n      <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center gap-3 mb-2\">\n            <div className=\"p-2 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-lg shadow-lg\">\n              <Camera className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Family Gallery\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                Mga memories ug moments sa inyong pamilya ug tindahan\n              </p>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4\">\n            <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-blue-600 dark:text-blue-400\">Total Photos</p>\n                  <p className=\"text-lg font-bold text-blue-900 dark:text-blue-300\">{galleryStats.totalPhotos}</p>\n                </div>\n                <ImageIcon className=\"h-5 w-5 text-blue-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-green-600 dark:text-green-400\">Total Views</p>\n                  <p className=\"text-lg font-bold text-green-900 dark:text-green-300\">{galleryStats.totalViews}</p>\n                </div>\n                <Eye className=\"h-5 w-5 text-green-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-3 rounded-lg border border-red-200 dark:border-red-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-red-600 dark:text-red-400\">Total Likes</p>\n                  <p className=\"text-lg font-bold text-red-900 dark:text-red-300\">{galleryStats.totalLikes}</p>\n                </div>\n                <Heart className=\"h-5 w-5 text-red-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-amber-700 dark:text-amber-400\">Selected</p>\n                  <p className=\"text-lg font-bold text-amber-900 dark:text-amber-300\">{selectedPhotos.length}</p>\n                </div>\n                <Activity className=\"h-5 w-5 text-amber-600\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-2\">\n          {selectedPhotos.length > 0 && (\n            <>\n              <button\n                onClick={handleBulkDelete}\n                className=\"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n                Delete Selected ({selectedPhotos.length})\n              </button>\n              <button\n                onClick={() => setSelectedPhotos([])}\n                className=\"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <X className=\"h-4 w-4\" />\n                Clear Selection\n              </button>\n            </>\n          )}\n\n          <button\n            onClick={() => setIsLoading(!isLoading)}\n            className=\"btn-outline flex items-center gap-2 text-sm\"\n            disabled={isLoading}\n          >\n            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n            Refresh\n          </button>\n\n          <button\n            onClick={() => setIsUploadModalOpen(true)}\n            className=\"btn-primary flex items-center gap-2\"\n          >\n            <Plus className=\"h-4 w-4\" />\n            Add Photo\n          </button>\n        </div>\n      </div>\n\n      {/* Enhanced Search and Filters */}\n      <div className=\"card p-6 border-l-4 border-l-amber-600\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n            <Search className=\"h-5 w-5 text-amber-600\" />\n            Search & Filters\n          </h3>\n          <div className=\"flex items-center gap-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors\"\n            >\n              <Filter className=\"h-4 w-4\" />\n              {showFilters ? 'Hide Filters' : 'Show Filters'}\n            </button>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${\n                  viewMode === 'grid'\n                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                }`}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${\n                  viewMode === 'list'\n                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                }`}\n              >\n                <List className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Basic Search and Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search photos, descriptions, tags...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n          >\n            {categories.map(category => (\n              <option key={category.id} value={category.id}>{category.label}</option>\n            ))}\n          </select>\n\n          {/* Sort Options */}\n          <div className=\"flex gap-2\">\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}\n              className=\"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            >\n              <option value=\"date\">Sort by Date</option>\n              <option value=\"likes\">Sort by Likes</option>\n              <option value=\"views\">Sort by Views</option>\n              <option value=\"title\">Sort by Title</option>\n            </select>\n            <button\n              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n              className=\"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}\n            >\n              {sortOrder === 'asc' ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />}\n            </button>\n          </div>\n\n          {/* Bulk Actions */}\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleSelectAll}\n              className=\"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm\"\n            >\n              {selectedPhotos.length === filteredAndSortedPhotos.length ? 'Deselect All' : 'Select All'}\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Filters (Collapsible) */}\n        {showFilters && (\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {categories.slice(1).map(category => {\n                const Icon = category.icon\n                const count = galleryStats.categoryCounts[category.id] || 0\n                return (\n                  <div key={category.id} className=\"text-center\">\n                    <button\n                      onClick={() => setSelectedCategory(category.id)}\n                      className={`w-full p-3 rounded-lg border-2 transition-all ${\n                        selectedCategory === category.id\n                          ? 'border-amber-600 bg-amber-50 dark:bg-amber-900/20'\n                          : 'border-gray-200 dark:border-gray-700 hover:border-amber-300'\n                      }`}\n                    >\n                      <Icon className={`h-6 w-6 mx-auto mb-2 ${\n                        selectedCategory === category.id ? 'text-amber-600' : 'text-gray-500'\n                      }`} />\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{category.label}</p>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">{count} photos</p>\n                    </button>\n                  </div>\n                )\n              })}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Photo Display */}\n      <div className=\"card overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n              <ImageIcon className=\"h-5 w-5 text-amber-600\" />\n              Photo Gallery ({filteredAndSortedPhotos.length})\n            </h3>\n\n            {selectedPhotos.length > 0 && (\n              <span className=\"text-xs bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 px-2 py-1 rounded-full\">\n                {selectedPhotos.length} selected\n              </span>\n            )}\n          </div>\n        </div>\n\n        {viewMode === 'grid' ? (\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {filteredAndSortedPhotos.map((photo, index) => (\n                <div key={photo.id} className=\"group relative\">\n                  {/* Selection Checkbox */}\n                  <div className=\"absolute top-2 left-2 z-10\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedPhotos.includes(photo.id)}\n                      onChange={() => handleSelectPhoto(photo.id)}\n                      className=\"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2\"\n                    />\n                  </div>\n\n                  {/* Category Badge */}\n                  <div className=\"absolute top-2 right-2 z-10\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                      photo.category === 'family' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :\n                      photo.category === 'store' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                      photo.category === 'events' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :\n                      photo.category === 'products' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400' :\n                      photo.category === 'customers' ? 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400' :\n                      'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'\n                    }`}>\n                      {categories.find(c => c.id === photo.category)?.label}\n                    </span>\n                  </div>\n\n                  <div className=\"card overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n                    <div className=\"relative aspect-video bg-gray-200 dark:bg-gray-700 cursor-pointer\" onClick={() => openLightbox(index)}>\n                      <div className=\"w-full h-full bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 flex items-center justify-center\">\n                        <ImageIcon className=\"h-16 w-16 text-gray-400\" />\n                      </div>\n\n                      {/* Hover Overlay */}\n                      <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100\">\n                        <div className=\"flex items-center gap-3\">\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              openLightbox(index)\n                            }}\n                            className=\"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors\"\n                          >\n                            <Eye className=\"h-4 w-4 inline mr-1\" />\n                            View\n                          </button>\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              setSelectedPhoto(photo)\n                            }}\n                            className=\"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors\"\n                          >\n                            <Settings className=\"h-4 w-4 inline mr-1\" />\n                            Details\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Views Counter */}\n                      <div className=\"absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center gap-1\">\n                        <Eye className=\"h-3 w-3\" />\n                        {photo.views}\n                      </div>\n                    </div>\n\n                    <div className=\"p-4\">\n                      <h3 className=\"font-semibold text-gray-900 dark:text-white mb-1 line-clamp-1\">\n                        {photo.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\">\n                        {photo.description}\n                      </p>\n\n                      {/* Tags */}\n                      {photo.tags.length > 0 && (\n                        <div className=\"flex flex-wrap gap-1 mb-3\">\n                          {photo.tags.slice(0, 3).map(tag => (\n                            <span key={tag} className=\"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md\">\n                              #{tag}\n                            </span>\n                          ))}\n                          {photo.tags.length > 3 && (\n                            <span className=\"text-xs text-gray-500 dark:text-gray-400\">+{photo.tags.length - 3} more</span>\n                          )}\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400\">\n                          <Clock className=\"h-3 w-3\" />\n                          {new Date(photo.date).toLocaleDateString('en-PH')}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                          <button\n                            onClick={() => handleLike(photo.id)}\n                            className={`flex items-center space-x-1 text-sm transition-colors ${\n                              photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400 hover:text-red-500'\n                            }`}\n                          >\n                            <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />\n                            <span>{photo.likes}</span>\n                          </button>\n\n                          <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors\">\n                            <Share2 className=\"h-4 w-4\" />\n                          </button>\n\n                          <div className=\"relative group\">\n                            <button className=\"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\">\n                              <MoreVertical className=\"h-4 w-4\" />\n                            </button>\n\n                            {/* Dropdown Menu */}\n                            <div className=\"absolute right-0 top-full mt-1 w-32 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10\">\n                              <button\n                                onClick={() => setSelectedPhoto(photo)}\n                                className=\"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2\"\n                              >\n                                <Edit className=\"h-3 w-3\" />\n                                Edit\n                              </button>\n                              <button\n                                onClick={() => handleDelete(photo.id)}\n                                className=\"w-full text-left px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2\"\n                              >\n                                <Trash2 className=\"h-3 w-3\" />\n                                Delete\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        ) : (\n          // List View\n          <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n            {filteredAndSortedPhotos.map((photo, index) => (\n              <div key={photo.id} className=\"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group\">\n                <div className=\"flex items-start space-x-4\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedPhotos.includes(photo.id)}\n                    onChange={() => handleSelectPhoto(photo.id)}\n                    className=\"mt-1 w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2\"\n                  />\n\n                  <div className=\"w-20 h-20 bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center cursor-pointer\" onClick={() => openLightbox(index)}>\n                    <ImageIcon className=\"h-8 w-8 text-gray-400\" />\n                  </div>\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-1\">\n                          {photo.title}\n                        </h3>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                          {photo.description}\n                        </p>\n\n                        <div className=\"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n                          <span className=\"flex items-center gap-1\">\n                            <Calendar className=\"h-3 w-3\" />\n                            {new Date(photo.date).toLocaleDateString('en-PH')}\n                          </span>\n                          <span className=\"flex items-center gap-1\">\n                            <Eye className=\"h-3 w-3\" />\n                            {photo.views} views\n                          </span>\n                          <span className=\"flex items-center gap-1\">\n                            <Heart className=\"h-3 w-3\" />\n                            {photo.likes} likes\n                          </span>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            photo.category === 'family' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :\n                            photo.category === 'store' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                            photo.category === 'events' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :\n                            photo.category === 'products' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400' :\n                            photo.category === 'customers' ? 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400' :\n                            'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'\n                          }`}>\n                            {categories.find(c => c.id === photo.category)?.label}\n                          </span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          onClick={() => openLightbox(index)}\n                          className=\"text-gray-500 dark:text-gray-400 hover:text-amber-600 transition-colors\"\n                        >\n                          <Eye className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleLike(photo.id)}\n                          className={`transition-colors ${\n                            photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400 hover:text-red-500'\n                          }`}\n                        >\n                          <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />\n                        </button>\n                        <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors\">\n                          <Share2 className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(photo.id)}\n                          className=\"text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {filteredAndSortedPhotos.length === 0 && (\n          <div className=\"p-16 text-center\">\n            <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\">\n              <ImageIcon className=\"h-12 w-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3\">\n              Walang Photos na Nakita\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              Try adjusting your search terms or filter criteria to find the photos you&apos;re looking for.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n              <button\n                onClick={() => {\n                  setSearchTerm('')\n                  setSelectedCategory('all')\n                }}\n                className=\"btn-outline\"\n              >\n                Clear Filters\n              </button>\n              <button\n                onClick={() => setIsUploadModalOpen(true)}\n                className=\"btn-primary\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add First Photo\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Upload Modal */}\n      {isUploadModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"p-3 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-xl shadow-lg\">\n                    <Upload className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                      Add New Photo\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      Upload a new photo to your family gallery\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setIsUploadModalOpen(false)}\n                  className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"h-5 w-5 text-gray-500\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <form onSubmit={handleUpload} className=\"p-6 space-y-6\">\n              {/* Drag and Drop File Upload */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Photo File\n                </label>\n                <div\n                  onDragOver={handleDragOver}\n                  onDrop={handleDrop}\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-amber-400 dark:hover:border-amber-500 transition-colors\"\n                >\n                  {uploadForm.file ? (\n                    <div className=\"space-y-2\">\n                      <ImageIcon className=\"h-12 w-12 text-amber-600 mx-auto\" />\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {uploadForm.file.name}\n                      </p>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        {(uploadForm.file.size / (1024 * 1024)).toFixed(1)} MB\n                      </p>\n                      <button\n                        type=\"button\"\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          setUploadForm({ ...uploadForm, file: null })\n                        }}\n                        className=\"text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                      >\n                        Remove file\n                      </button>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-2\">\n                      <Upload className=\"h-12 w-12 text-gray-400 mx-auto\" />\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        Click to upload or drag and drop\n                      </p>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        PNG, JPG, GIF up to 10MB\n                      </p>\n                    </div>\n                  )}\n                </div>\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleFileChange}\n                  className=\"hidden\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Title */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Title\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={uploadForm.title}\n                    onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}\n                    placeholder=\"Enter photo title...\"\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700\"\n                    required\n                  />\n                </div>\n\n                {/* Category */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Category\n                  </label>\n                  <select\n                    value={uploadForm.category}\n                    onChange={(e) => setUploadForm({ ...uploadForm, category: e.target.value as Photo['category'] })}\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700\"\n                  >\n                    {categories.slice(1).map(category => (\n                      <option key={category.id} value={category.id}>{category.label}</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Description */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Description\n                </label>\n                <textarea\n                  value={uploadForm.description}\n                  onChange={(e) => setUploadForm({ ...uploadForm, description: e.target.value })}\n                  rows={4}\n                  placeholder=\"Describe your photo...\"\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Tags */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Tags (comma separated)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={uploadForm.tags}\n                    onChange={(e) => setUploadForm({ ...uploadForm, tags: e.target.value })}\n                    placeholder=\"family, celebration, milestone...\"\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700\"\n                  />\n                </div>\n\n                {/* Location */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Location (optional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={uploadForm.location}\n                    onChange={(e) => setUploadForm({ ...uploadForm, location: e.target.value })}\n                    placeholder=\"Cebu City, Philippines...\"\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700\"\n                  />\n                </div>\n              </div>\n\n              {/* Privacy Setting */}\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isPrivate\"\n                  checked={uploadForm.isPrivate}\n                  onChange={(e) => setUploadForm({ ...uploadForm, isPrivate: e.target.checked })}\n                  className=\"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2\"\n                />\n                <label htmlFor=\"isPrivate\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                  Make this photo private (only visible to family members)\n                </label>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsUploadModalOpen(false)}\n                  className=\"flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 font-medium transition-colors\"\n                  disabled={isLoading}\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-gradient-to-r from-amber-600 to-yellow-700 hover:from-amber-700 hover:to-yellow-800 text-white px-6 py-3 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg\"\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <>\n                      <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                      Uploading...\n                    </>\n                  ) : (\n                    <>\n                      <Upload className=\"h-4 w-4\" />\n                      Upload Photo\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Enhanced Lightbox Modal */}\n      {isLightboxOpen && filteredAndSortedPhotos[currentPhotoIndex] && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 animate-fade-in\">\n          <div className=\"relative w-full h-full flex items-center justify-center p-4\">\n            {/* Close Button */}\n            <button\n              onClick={closeLightbox}\n              className=\"absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n\n            {/* Navigation Buttons */}\n            {filteredAndSortedPhotos.length > 1 && (\n              <>\n                <button\n                  onClick={prevPhoto}\n                  className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all\"\n                >\n                  <ChevronLeft className=\"h-8 w-8\" />\n                </button>\n                <button\n                  onClick={nextPhoto}\n                  className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all\"\n                >\n                  <ChevronRight className=\"h-8 w-8\" />\n                </button>\n              </>\n            )}\n\n            {/* Slideshow Controls */}\n            <div className=\"absolute top-4 left-4 z-10 flex items-center gap-2\">\n              <button\n                onClick={toggleSlideshow}\n                className=\"p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all\"\n              >\n                {isSlideshow ? <Pause className=\"h-5 w-5\" /> : <Play className=\"h-5 w-5\" />}\n              </button>\n              <span className=\"text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full\">\n                {currentPhotoIndex + 1} / {filteredAndSortedPhotos.length}\n              </span>\n            </div>\n\n            {/* Main Image */}\n            <div className=\"max-w-4xl max-h-full flex items-center justify-center\">\n              <div className=\"relative bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center\" style={{ minWidth: '600px', minHeight: '400px' }}>\n                <ImageIcon className=\"h-32 w-32 text-gray-400\" />\n\n                {/* Photo Info Overlay */}\n                <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white\">\n                  <h3 className=\"text-xl font-semibold mb-2\">\n                    {filteredAndSortedPhotos[currentPhotoIndex].title}\n                  </h3>\n                  <p className=\"text-sm opacity-90 mb-3\">\n                    {filteredAndSortedPhotos[currentPhotoIndex].description}\n                  </p>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-4 text-sm\">\n                      <span className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        {new Date(filteredAndSortedPhotos[currentPhotoIndex].date).toLocaleDateString('en-PH')}\n                      </span>\n                      <span className=\"flex items-center gap-1\">\n                        <Eye className=\"h-4 w-4\" />\n                        {filteredAndSortedPhotos[currentPhotoIndex].views}\n                      </span>\n                      <span className=\"flex items-center gap-1\">\n                        <Heart className=\"h-4 w-4\" />\n                        {filteredAndSortedPhotos[currentPhotoIndex].likes}\n                      </span>\n                    </div>\n\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() => handleLike(filteredAndSortedPhotos[currentPhotoIndex].id)}\n                        className={`p-2 rounded-full transition-all ${\n                          filteredAndSortedPhotos[currentPhotoIndex].isLiked\n                            ? 'bg-red-500 text-white'\n                            : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'\n                        }`}\n                      >\n                        <Heart className={`h-4 w-4 ${filteredAndSortedPhotos[currentPhotoIndex].isLiked ? 'fill-current' : ''}`} />\n                      </button>\n                      <button className=\"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all\">\n                        <Share2 className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all\">\n                        <Download className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Enhanced Photo Detail Modal */}\n      {selectedPhoto && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div className={`p-3 rounded-xl shadow-lg ${\n                    selectedPhoto.category === 'family' ? 'bg-blue-500' :\n                    selectedPhoto.category === 'store' ? 'bg-green-500' :\n                    selectedPhoto.category === 'events' ? 'bg-yellow-500' :\n                    selectedPhoto.category === 'products' ? 'bg-amber-600' :\n                    selectedPhoto.category === 'customers' ? 'bg-pink-500' :\n                    'bg-orange-500'\n                  }`}>\n                    <ImageIcon className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                      Photo Details\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {selectedPhoto.title}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setSelectedPhoto(null)}\n                  className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"h-5 w-5 text-gray-500\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <div className=\"p-6\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Photo Preview */}\n                <div>\n                  <div className=\"aspect-video bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center\">\n                    <ImageIcon className=\"h-24 w-24 text-gray-400\" />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-4\">\n                      <button\n                        onClick={() => handleLike(selectedPhoto.id)}\n                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${\n                          selectedPhoto.isLiked\n                            ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400'\n                            : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-red-50 hover:text-red-600'\n                        }`}\n                      >\n                        <Heart className={`h-4 w-4 ${selectedPhoto.isLiked ? 'fill-current' : ''}`} />\n                        {selectedPhoto.likes}\n                      </button>\n\n                      <button className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all\">\n                        <Share2 className=\"h-4 w-4\" />\n                        Share\n                      </button>\n\n                      <button className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all\">\n                        <Download className=\"h-4 w-4\" />\n                        Download\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Photo Information */}\n                <div className=\"space-y-6\">\n                  {/* Basic Info */}\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      Information\n                    </h4>\n\n                    <div className=\"space-y-3\">\n                      <div>\n                        <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                          Title\n                        </label>\n                        <p className=\"text-sm text-gray-900 dark:text-white mt-1\">\n                          {selectedPhoto.title}\n                        </p>\n                      </div>\n\n                      <div>\n                        <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                          Description\n                        </label>\n                        <p className=\"text-sm text-gray-900 dark:text-white mt-1\">\n                          {selectedPhoto.description || 'No description provided'}\n                        </p>\n                      </div>\n\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                            Category\n                          </label>\n                          <p className=\"text-sm text-gray-900 dark:text-white mt-1\">\n                            {categories.find(c => c.id === selectedPhoto.category)?.label}\n                          </p>\n                        </div>\n\n                        <div>\n                          <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                            Date\n                          </label>\n                          <p className=\"text-sm text-gray-900 dark:text-white mt-1\">\n                            {new Date(selectedPhoto.date).toLocaleDateString('en-PH')}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Statistics */}\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      Statistics\n                    </h4>\n\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                        <Eye className=\"h-5 w-5 text-blue-500 mx-auto mb-1\" />\n                        <p className=\"text-lg font-bold text-gray-900 dark:text-white\">{selectedPhoto.views}</p>\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400\">Views</p>\n                      </div>\n\n                      <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                        <Heart className=\"h-5 w-5 text-red-500 mx-auto mb-1\" />\n                        <p className=\"text-lg font-bold text-gray-900 dark:text-white\">{selectedPhoto.likes}</p>\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400\">Likes</p>\n                      </div>\n\n                      <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                        <Share2 className=\"h-5 w-5 text-green-500 mx-auto mb-1\" />\n                        <p className=\"text-lg font-bold text-gray-900 dark:text-white\">0</p>\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400\">Shares</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Tags */}\n                  {selectedPhoto.tags.length > 0 && (\n                    <div>\n                      <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                        Tags\n                      </h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedPhoto.tags.map(tag => (\n                          <span\n                            key={tag}\n                            className=\"inline-flex px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-400 text-sm rounded-full\"\n                          >\n                            #{tag}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Technical Details */}\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      Technical Details\n                    </h4>\n\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">File Size:</span>\n                        <span className=\"text-gray-900 dark:text-white\">{selectedPhoto.fileSize}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">Dimensions:</span>\n                        <span className=\"text-gray-900 dark:text-white\">{selectedPhoto.dimensions}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">Uploaded by:</span>\n                        <span className=\"text-gray-900 dark:text-white\">{selectedPhoto.uploadedBy}</span>\n                      </div>\n                      {selectedPhoto.location && (\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-500 dark:text-gray-400\">Location:</span>\n                          <span className=\"text-gray-900 dark:text-white\">{selectedPhoto.location}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA6Be,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAW;gBAAU;aAAY;YACxC,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAe;gBAAe;aAAY;YACjD,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAa;gBAAY;aAAgB;YAChD,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAY;gBAAY;aAAY;YAC3C,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;QACd;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAU;gBAAW;aAAe;YAC3C,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;QACd;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAa;gBAAW;aAAY;YAC3C,OAAO;YACP,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;QACd;KACD;IAED,4BAA4B;IAC5B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;QACX,UAAU;IACZ;IAEA,uCAAuC;IACvC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YACtC,IAAI,WAAW,OAAO,MAAM;2EAAC,CAAA;oBAC3B,MAAM,gBAAgB,eAAe,MACnC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,MAAM,IAAI,CAAC,IAAI;mFAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;oBAE1E,MAAM,kBAAkB,qBAAqB,SAAS,MAAM,QAAQ,KAAK;oBAEzE,OAAO,iBAAiB;gBAC1B;;YAEA,SAAS,IAAI;kEAAC,CAAC,GAAG;oBAChB,IAAI,aAAa;oBACjB,OAAQ;wBACN,KAAK;4BACH,aAAa,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;4BAClE;wBACF,KAAK;4BACH,aAAa,EAAE,KAAK,GAAG,EAAE,KAAK;4BAC9B;wBACF,KAAK;4BACH,aAAa,EAAE,KAAK,GAAG,EAAE,KAAK;4BAC9B;wBACF,KAAK;4BACH,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;4BAC1C;oBACJ;oBACA,OAAO,cAAc,SAAS,CAAC,aAAa;gBAC9C;;YAEA,OAAO;QACT;yDAAG;QAAC;QAAQ;QAAY;QAAkB;QAAQ;KAAU;IAE5D,8BAA8B;IAC9B,MAAM,aAAa,CAAC;QAClB,UAAU,OAAO,GAAG,CAAC,CAAA,QACnB,MAAM,EAAE,KAAK,UACT;gBACE,GAAG,KAAK;gBACR,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG;gBACvD,SAAS,CAAC,MAAM,OAAO;YACzB,IACA;IAER;IAEA,MAAM,aAAa,CAAC;QAClB,UAAU,OAAO,GAAG,CAAC,CAAA,QACnB,MAAM,EAAE,KAAK,UACT;gBAAE,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK,GAAG;YAAE,IACnC;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,8CAA8C;YACxD,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YAC9C,kBAAkB,eAAe,MAAM,CAAC,CAAA,KAAM,OAAO;QACvD;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,eAAe,MAAM,GAAG,KAAK,QAAQ,CAAC,gCAAgC,EAAE,eAAe,MAAM,CAAC,WAAW,CAAC,GAAG;YAC/G,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,eAAe,QAAQ,CAAC,MAAM,EAAE;YAClE,kBAAkB,EAAE;QACtB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,kBAAkB;QACtB,IAAI,eAAe,MAAM,KAAK,wBAAwB,MAAM,EAAE;YAC5D,kBAAkB,EAAE;QACtB,OAAO;YACL,kBAAkB,wBAAwB,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;QACjE;IACF;IAEA,kCAAkC;IAClC,MAAM,eAAe,CAAC;QACpB,qBAAqB;QACrB,kBAAkB;QAClB,WAAW,uBAAuB,CAAC,WAAW,CAAC,EAAE;IACnD;IAEA,MAAM,gBAAgB;QACpB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,YAAY;QAChB,MAAM,YAAY,CAAC,oBAAoB,CAAC,IAAI,wBAAwB,MAAM;QAC1E,qBAAqB;QACrB,WAAW,uBAAuB,CAAC,UAAU,CAAC,EAAE;IAClD;IAEA,MAAM,YAAY;QAChB,MAAM,YAAY,sBAAsB,IAAI,wBAAwB,MAAM,GAAG,IAAI,oBAAoB;QACrG,qBAAqB;QACrB,WAAW,uBAAuB,CAAC,UAAU,CAAC,EAAE;IAClD;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAC;IAClB;IAEA,gCAAgC;IAChC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI,WAAW,KAAK,EAAE;YACvC,aAAa;YAEb,wBAAwB;YACxB,WAAW;gBACT,MAAM,WAAkB;oBACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,KAAK,IAAI,eAAe,CAAC,WAAW,IAAI;oBACxC,OAAO,WAAW,KAAK;oBACvB,aAAa,WAAW,WAAW;oBACnC,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;oBAChD,OAAO;oBACP,SAAS;oBACT,UAAU,WAAW,QAAQ;oBAC7B,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO;oBACtE,OAAO;oBACP,WAAW,WAAW,SAAS;oBAC/B,YAAY;oBACZ,UAAU,GAAG,CAAC,WAAW,IAAI,CAAE,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;oBACpE,YAAY;oBACZ,UAAU,WAAW,QAAQ;gBAC/B;gBAEA,UAAU;oBAAC;uBAAa;iBAAO;gBAC/B,cAAc;oBACZ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,UAAU;gBACZ;gBACA,qBAAqB;gBACrB,aAAa;YACf,GAAG;QACL;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,cAAc;gBAAE,GAAG,UAAU;gBAAE;YAAK;QACtC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QACpC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,cAAc;gBAAE,GAAG,UAAU;gBAAE;YAAK;QACtC;IACF;IAEA,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC3B,MAAM,aAAa,OAAO,MAAM;kEAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK;iEAAE;YACpE,MAAM,aAAa,OAAO,MAAM;kEAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK;iEAAE;YACpE,MAAM,iBAAiB,OAAO,MAAM;sEAAC,CAAC,KAAK;oBACzC,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI;oBACnD,OAAO;gBACT;qEAAG,CAAC;YAEJ,OAAO;gBACL,aAAa,OAAO,MAAM;gBAC1B;gBACA;gBACA;gBACA,cAAc,KAAK,KAAK,CAAC,aAAa,OAAO,MAAM,KAAK;gBACxD,aAAa,OAAO,MAAM;2DAAC,CAAC,MAAM,UAAY,AAAC,KAAK,KAAK,GAAG,QAAQ,KAAK,GAAI,OAAO;0DAAS,MAAM,CAAC,EAAE;YACxG;QACF;8CAAG;QAAC;KAAO;IAEX,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,OAAO;YAAS,MAAM,4MAAA,CAAA,OAAI;QAAC;QACxC;YAAE,IAAI;YAAU,OAAO;YAAW,MAAM,uMAAA,CAAA,QAAK;QAAC;QAC9C;YAAE,IAAI;YAAS,OAAO;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;QACnD;YAAE,IAAI;YAAU,OAAO;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACnD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,mMAAA,CAAA,MAAG;QAAC;QAC/C;YAAE,IAAI;YAAa,OAAO;YAAY,MAAM,uMAAA,CAAA,QAAK;QAAC;QAClD;YAAE,IAAI;YAAgB,OAAO;YAAe,MAAM,qMAAA,CAAA,OAAI;QAAC;KACxD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAOpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAAsD,aAAa,WAAW;;;;;;;;;;;;8DAE7F,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAyD;;;;;;sEACtE,6LAAC;4DAAE,WAAU;sEAAwD,aAAa,UAAU;;;;;;;;;;;;8DAE9F,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqD;;;;;;sEAClE,6LAAC;4DAAE,WAAU;sEAAoD,aAAa,UAAU;;;;;;;;;;;;8DAE1F,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAyD;;;;;;sEACtE,6LAAC;4DAAE,WAAU;sEAAwD,eAAe,MAAM;;;;;;;;;;;;8DAE5F,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,MAAM,GAAG,mBACvB;;kDACE,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;4CACZ,eAAe,MAAM;4CAAC;;;;;;;kDAE1C,6LAAC;wCACC,SAAS,IAAM,kBAAkB,EAAE;wCACnC,WAAU;;0DAEV,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;0CAM/B,6LAAC;gCACC,SAAS,IAAM,aAAa,CAAC;gCAC7B,WAAU;gCACV,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;0CAIxE,6LAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,cAAc,iBAAiB;;;;;;;kDAIlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,wDAAwD,EAClE,aAAa,SACT,sEACA,8EACJ;0DAEF,cAAA,6LAAC,4MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,wDAAwD,EAClE,aAAa,SACT,sEACA,8EACJ;0DAEF,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;0CAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wCAAyB,OAAO,SAAS,EAAE;kDAAG,SAAS,KAAK;uCAAhD,SAAS,EAAE;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;kDAExB,6LAAC;wCACC,SAAS,IAAM,aAAa,cAAc,QAAQ,SAAS;wCAC3D,WAAU;wCACV,OAAO,CAAC,KAAK,EAAE,cAAc,QAAQ,eAAe,aAAa;kDAEhE,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAe,6LAAC,oOAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,eAAe,MAAM,KAAK,wBAAwB,MAAM,GAAG,iBAAiB;;;;;;;;;;;;;;;;;oBAMlF,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA;gCACvB,MAAM,OAAO,SAAS,IAAI;gCAC1B,MAAM,QAAQ,aAAa,cAAc,CAAC,SAAS,EAAE,CAAC,IAAI;gCAC1D,qBACE,6LAAC;oCAAsB,WAAU;8CAC/B,cAAA,6LAAC;wCACC,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAW,CAAC,8CAA8C,EACxD,qBAAqB,SAAS,EAAE,GAC5B,sDACA,+DACJ;;0DAEF,6LAAC;gDAAK,WAAW,CAAC,qBAAqB,EACrC,qBAAqB,SAAS,EAAE,GAAG,mBAAmB,iBACtD;;;;;;0DACF,6LAAC;gDAAE,WAAU;0DAAqD,SAAS,KAAK;;;;;;0DAChF,6LAAC;gDAAE,WAAU;;oDAA4C;oDAAM;;;;;;;;;;;;;mCAbzD,SAAS,EAAE;;;;;4BAiBzB;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;wCAA2B;wCAChC,wBAAwB,MAAM;wCAAC;;;;;;;gCAGhD,eAAe,MAAM,GAAG,mBACvB,6LAAC;oCAAK,WAAU;;wCACb,eAAe,MAAM;wCAAC;;;;;;;;;;;;;;;;;;oBAM9B,aAAa,uBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,wBAAwB,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC;oCAAmB,WAAU;;sDAE5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,SAAS,eAAe,QAAQ,CAAC,MAAM,EAAE;gDACzC,UAAU,IAAM,kBAAkB,MAAM,EAAE;gDAC1C,WAAU;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,QAAQ,KAAK,WAAW,qEAC9B,MAAM,QAAQ,KAAK,UAAU,yEAC7B,MAAM,QAAQ,KAAK,WAAW,6EAC9B,MAAM,QAAQ,KAAK,aAAa,yEAChC,MAAM,QAAQ,KAAK,cAAc,qEACjC,4EACA;0DACC,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,QAAQ,GAAG;;;;;;;;;;;sDAIpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAoE,SAAS,IAAM,aAAa;;sEAC7G,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;gEAAC,WAAU;;;;;;;;;;;sEAIvB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,aAAa;wEACf;wEACA,WAAU;;0FAEV,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAwB;;;;;;;kFAGzC,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,iBAAiB;wEACnB;wEACA,WAAU;;0FAEV,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAwB;;;;;;;;;;;;;;;;;;sEAOlD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,MAAM,KAAK;;;;;;;;;;;;;8DAIhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;sEAEd,6LAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;wDAInB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,oBAC1B,6LAAC;wEAAe,WAAU;;4EAAyG;4EAC/H;;uEADO;;;;;gEAIZ,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;oEAAK,WAAU;;wEAA2C;wEAAE,MAAM,IAAI,CAAC,MAAM,GAAG;wEAAE;;;;;;;;;;;;;sEAKzF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC;;;;;;;8EAG3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,SAAS,IAAM,WAAW,MAAM,EAAE;4EAClC,WAAW,CAAC,sDAAsD,EAChE,MAAM,OAAO,GAAG,iBAAiB,uDACjC;;8FAEF,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI;;;;;;8FAClE,6LAAC;8FAAM,MAAM,KAAK;;;;;;;;;;;;sFAGpB,6LAAC;4EAAO,WAAU;sFAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;sFAGpB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAO,WAAU;8FAChB,cAAA,6LAAC,6NAAA,CAAA,eAAY;wFAAC,WAAU;;;;;;;;;;;8FAI1B,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FACC,SAAS,IAAM,iBAAiB;4FAChC,WAAU;;8GAEV,6LAAC,8MAAA,CAAA,OAAI;oGAAC,WAAU;;;;;;gGAAY;;;;;;;sGAG9B,6LAAC;4FACC,SAAS,IAAM,aAAa,MAAM,EAAE;4FACpC,WAAU;;8GAEV,6LAAC,6MAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;gGAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA7HpC,MAAM,EAAE;;;;;;;;;;;;;;+BA2IxB,YAAY;kCACZ,6LAAC;wBAAI,WAAU;kCACZ,wBAAwB,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC;gCAAmB,WAAU;0CAC5B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,eAAe,QAAQ,CAAC,MAAM,EAAE;4CACzC,UAAU,IAAM,kBAAkB,MAAM,EAAE;4CAC1C,WAAU;;;;;;sDAGZ,6LAAC;4CAAI,WAAU;4CAA6J,SAAS,IAAM,aAAa;sDACtM,cAAA,6LAAC,uMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,MAAM,KAAK;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EACV,MAAM,WAAW;;;;;;0EAGpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC;;;;;;;kFAE3C,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EACd,MAAM,KAAK;4EAAC;;;;;;;kFAEf,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,MAAM,KAAK;4EAAC;;;;;;;kFAEf,6LAAC;wEAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,QAAQ,KAAK,WAAW,qEAC9B,MAAM,QAAQ,KAAK,UAAU,yEAC7B,MAAM,QAAQ,KAAK,WAAW,6EAC9B,MAAM,QAAQ,KAAK,aAAa,yEAChC,MAAM,QAAQ,KAAK,cAAc,qEACjC,4EACA;kFACC,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;kEAKtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,aAAa;gEAC5B,WAAU;0EAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,6LAAC;gEACC,SAAS,IAAM,WAAW,MAAM,EAAE;gEAClC,WAAW,CAAC,kBAAkB,EAC5B,MAAM,OAAO,GAAG,iBAAiB,uDACjC;0EAEF,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI;;;;;;;;;;;0EAEpE,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC;gEACC,SAAS,IAAM,aAAa,MAAM,EAAE;gEACpC,WAAU;0EAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAvEpB,MAAM,EAAE;;;;;;;;;;oBAmFvB,wBAAwB,MAAM,KAAK,mBAClC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,cAAc;4CACd,oBAAoB;wCACtB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK5D,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,YAAY;4CACZ,QAAQ;4CACR,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,WAAU;sDAET,WAAW,IAAI,iBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAE,WAAU;kEACV,WAAW,IAAI,CAAC,IAAI;;;;;;kEAEvB,6LAAC;wDAAE,WAAU;;4DACV,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAErD,6LAAC;wDACC,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,cAAc;gEAAE,GAAG,UAAU;gEAAE,MAAM;4DAAK;wDAC5C;wDACA,WAAU;kEACX;;;;;;;;;;;qEAKH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAE,WAAU;kEAAoD;;;;;;kEAGjE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;sDAM9D,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,KAAK;oDACvB,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtE,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAKZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,OAAO,WAAW,QAAQ;oDAC1B,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAsB;oDAC9F,WAAU;8DAET,WAAW,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA,yBACvB,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEAAG,SAAS,KAAK;2DAAhD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAOhC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,WAAW,WAAW;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5E,MAAM;4CACN,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,IAAI;oDACtB,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACrE,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAKd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,QAAQ;oDAC1B,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACzE,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS,WAAW,SAAS;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,WAAW,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC5E,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAAgD;;;;;;;;;;;;8CAMvF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,0BACC;;kEACE,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAyB;;6EAIhD;;kEACE,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAY7C,kBAAkB,uBAAuB,CAAC,kBAAkB,kBAC3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;wBAId,wBAAwB,MAAM,GAAG,mBAChC;;8CACE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;sCAM9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,4BAAc,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAAe,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAEjE,6LAAC;oCAAK,WAAU;;wCACb,oBAAoB;wCAAE;wCAAI,wBAAwB,MAAM;;;;;;;;;;;;;sCAK7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAA6I,OAAO;oCAAE,UAAU;oCAAS,WAAW;gCAAQ;;kDACzM,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,uBAAuB,CAAC,kBAAkB,CAAC,KAAK;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,uBAAuB,CAAC,kBAAkB,CAAC,WAAW;;;;;;0DAGzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,EAAE,kBAAkB,CAAC;;;;;;;0EAEhF,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd,uBAAuB,CAAC,kBAAkB,CAAC,KAAK;;;;;;;0EAEnD,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,uBAAuB,CAAC,kBAAkB,CAAC,KAAK;;;;;;;;;;;;;kEAIrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,WAAW,uBAAuB,CAAC,kBAAkB,CAAC,EAAE;gEACvE,WAAW,CAAC,gCAAgC,EAC1C,uBAAuB,CAAC,kBAAkB,CAAC,OAAO,GAC9C,0BACA,yDACJ;0EAEF,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,QAAQ,EAAE,uBAAuB,CAAC,kBAAkB,CAAC,OAAO,GAAG,iBAAiB,IAAI;;;;;;;;;;;0EAEzG,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYrC,+BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,yBAAyB,EACxC,cAAc,QAAQ,KAAK,WAAW,gBACtC,cAAc,QAAQ,KAAK,UAAU,iBACrC,cAAc,QAAQ,KAAK,WAAW,kBACtC,cAAc,QAAQ,KAAK,aAAa,iBACxC,cAAc,QAAQ,KAAK,cAAc,gBACzC,iBACA;0DACA,cAAA,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,6LAAC;wDAAE,WAAU;kEACV,cAAc,KAAK;;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;;;;;;0DAGvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,WAAW,cAAc,EAAE;4DAC1C,WAAW,CAAC,4DAA4D,EACtE,cAAc,OAAO,GACjB,gEACA,oGACJ;;8EAEF,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,OAAO,GAAG,iBAAiB,IAAI;;;;;;gEACzE,cAAc,KAAK;;;;;;;sEAGtB,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAIhC,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAQxC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2D;;;;;;kEAIzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+E;;;;;;kFAGhG,6LAAC;wEAAE,WAAU;kFACV,cAAc,KAAK;;;;;;;;;;;;0EAIxB,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+E;;;;;;kFAGhG,6LAAC;wEAAE,WAAU;kFACV,cAAc,WAAW,IAAI;;;;;;;;;;;;0EAIlC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+E;;;;;;0FAGhG,6LAAC;gFAAE,WAAU;0FACV,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,QAAQ,GAAG;;;;;;;;;;;;kFAI5D,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+E;;;;;;0FAGhG,6LAAC;gFAAE,WAAU;0FACV,IAAI,KAAK,cAAc,IAAI,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQ3D,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2D;;;;;;kEAIzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,6LAAC;wEAAE,WAAU;kFAAmD,cAAc,KAAK;;;;;;kFACnF,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;;0EAG1D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;wEAAE,WAAU;kFAAmD,cAAc,KAAK;;;;;;kFACnF,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;;0EAG1D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;wEAAE,WAAU;kFAAkD;;;;;;kFAC/D,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;;;;;;;;;;;;;;4CAM7D,cAAc,IAAI,CAAC,MAAM,GAAG,mBAC3B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2D;;;;;;kEAGzE,6LAAC;wDAAI,WAAU;kEACZ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,oBACtB,6LAAC;gEAEC,WAAU;;oEACX;oEACG;;+DAHG;;;;;;;;;;;;;;;;0DAWf,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2D;;;;;;kEAIzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAiC,cAAc,QAAQ;;;;;;;;;;;;0EAEzE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAiC,cAAc,UAAU;;;;;;;;;;;;0EAE3E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAiC,cAAc,UAAU;;;;;;;;;;;;4DAE1E,cAAc,QAAQ,kBACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAiC,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajG;GA10CwB;KAAA", "debugId": null}}]}