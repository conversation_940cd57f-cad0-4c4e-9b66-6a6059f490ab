{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport {\n  Search,\n  Home,\n  Package,\n  Users,\n  Image,\n  Moon,\n  Sun,\n  LogOut,\n  User,\n  Filter,\n  Clock,\n  X,\n  Command,\n  ArrowRight,\n  Star,\n  History,\n  Zap,\n  Bell,\n  AlertTriangle,\n  CheckCircle,\n  Info,\n  Volume2,\n  VolumeX\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AdminHeaderProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [isSearchFocused, setIsSearchFocused] = useState(false)\n  const [searchResults, setSearchResults] = useState<any[]>([])\n  const [searchHistory, setSearchHistory] = useState<string[]>([])\n  const [isSearchLoading, setIsSearchLoading] = useState(false)\n  const [showSearchDropdown, setShowSearchDropdown] = useState(false)\n  const { setTheme, resolvedTheme } = useTheme()\n  const [isProfileOpen, setIsProfileOpen] = useState(false)\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)\n  const [notifications, setNotifications] = useState<any[]>([])\n  const [unreadCount, setUnreadCount] = useState(0)\n  const [soundEnabled, setSoundEnabled] = useState(true)\n  const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const { user, logout } = useAuth()\n  const searchRef = useRef<HTMLDivElement>(null)\n  const searchInputRef = useRef<HTMLInputElement>(null)\n  const notificationRef = useRef<HTMLDivElement>(null)\n  const quickActionsRef = useRef<HTMLDivElement>(null)\n\n  // Handle hydration\n  useEffect(() => {\n    setMounted(true)\n    // Load search history from localStorage\n    const savedHistory = localStorage.getItem('searchHistory')\n    if (savedHistory) {\n      setSearchHistory(JSON.parse(savedHistory))\n    }\n\n    // Load notification preferences\n    const soundPref = localStorage.getItem('notificationSound')\n    if (soundPref !== null) {\n      setSoundEnabled(JSON.parse(soundPref))\n    }\n\n    // Initialize notifications\n    fetchNotifications()\n  }, [])\n\n  // Fetch notifications\n  const fetchNotifications = async () => {\n    try {\n      // Simulate fetching notifications\n      const mockNotifications = [\n        {\n          id: 1,\n          type: 'warning',\n          title: 'Low Stock Alert',\n          message: '5 products are running low on stock',\n          timestamp: new Date(Date.now() - 10 * 60 * 1000),\n          read: false,\n          icon: AlertTriangle,\n          color: 'orange'\n        },\n        {\n          id: 2,\n          type: 'info',\n          title: 'New Debt Record',\n          message: 'Customer Juan added a new debt of ₱150.00',\n          timestamp: new Date(Date.now() - 30 * 60 * 1000),\n          read: false,\n          icon: Users,\n          color: 'blue'\n        },\n        {\n          id: 3,\n          type: 'success',\n          title: 'Payment Received',\n          message: 'Maria paid ₱200.00 towards her debt',\n          timestamp: new Date(Date.now() - 60 * 60 * 1000),\n          read: true,\n          icon: CheckCircle,\n          color: 'green'\n        }\n      ]\n\n      setNotifications(mockNotifications)\n      setUnreadCount(mockNotifications.filter(n => !n.read).length)\n    } catch (error) {\n      console.error('Error fetching notifications:', error)\n    }\n  }\n\n  // Play notification sound\n  const playNotificationSound = () => {\n    if (soundEnabled && mounted) {\n      // Create a simple notification sound\n      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()\n      const oscillator = audioContext.createOscillator()\n      const gainNode = audioContext.createGain()\n\n      oscillator.connect(gainNode)\n      gainNode.connect(audioContext.destination)\n\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)\n      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)\n\n      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)\n\n      oscillator.start(audioContext.currentTime)\n      oscillator.stop(audioContext.currentTime + 0.2)\n    }\n  }\n\n  // Mark notification as read\n  const markAsRead = (notificationId: number) => {\n    setNotifications(prev =>\n      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n    )\n    setUnreadCount(prev => Math.max(0, prev - 1))\n  }\n\n  // Mark all as read\n  const markAllAsRead = () => {\n    setNotifications(prev => prev.map(n => ({ ...n, read: true })))\n    setUnreadCount(0)\n  }\n\n  // Toggle sound\n  const toggleSound = () => {\n    const newSoundEnabled = !soundEnabled\n    setSoundEnabled(newSoundEnabled)\n    localStorage.setItem('notificationSound', JSON.stringify(newSoundEnabled))\n  }\n\n  // Handle click outside dropdowns\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setShowSearchDropdown(false)\n        setIsSearchFocused(false)\n      }\n      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {\n        setIsNotificationsOpen(false)\n      }\n      if (quickActionsRef.current && !quickActionsRef.current.contains(event.target as Node)) {\n        setIsQuickActionsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Simulate real-time notifications\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Randomly add new notifications (simulate real-time updates)\n      if (Math.random() < 0.1) { // 10% chance every 30 seconds\n        const newNotification = {\n          id: Date.now(),\n          type: 'info',\n          title: 'System Update',\n          message: 'Dashboard data has been refreshed',\n          timestamp: new Date(),\n          read: false,\n          icon: Info,\n          color: 'blue'\n        }\n\n        setNotifications(prev => [newNotification, ...prev.slice(0, 9)]) // Keep only 10 notifications\n        setUnreadCount(prev => prev + 1)\n        playNotificationSound()\n      }\n    }, 30000) // Check every 30 seconds\n\n    return () => clearInterval(interval)\n  }, [soundEnabled, mounted])\n\n  // Advanced search functionality\n  useEffect(() => {\n    const performSearch = async () => {\n      if (searchQuery.length < 2) {\n        setSearchResults([])\n        return\n      }\n\n      setIsSearchLoading(true)\n      try {\n        // Simulate API calls for search\n        const [productsRes, debtsRes] = await Promise.all([\n          fetch(`/api/products?search=${encodeURIComponent(searchQuery)}`),\n          fetch(`/api/debts?search=${encodeURIComponent(searchQuery)}`)\n        ])\n\n        const productsData = await productsRes.json()\n        const debtsData = await debtsRes.json()\n\n        const results = [\n          ...((productsData.products || []).slice(0, 3).map((item: any) => ({\n            ...item,\n            type: 'product',\n            icon: Package\n          }))),\n          ...((debtsData.debts || []).slice(0, 3).map((item: any) => ({\n            ...item,\n            type: 'debt',\n            icon: Users\n          })))\n        ]\n\n        setSearchResults(results)\n      } catch (error) {\n        console.error('Search error:', error)\n        setSearchResults([])\n      } finally {\n        setIsSearchLoading(false)\n      }\n    }\n\n    const debounceTimer = setTimeout(performSearch, 300)\n    return () => clearTimeout(debounceTimer)\n  }, [searchQuery])\n\n\n\n  const navigationItems = [\n    {\n      id: 'dashboard',\n      label: 'Home Dashboard',\n      icon: Home,\n      tooltip: 'Dashboard Overview'\n    },\n    {\n      id: 'products',\n      label: 'Product Lists',\n      icon: Package,\n      tooltip: 'Manage Products'\n    },\n    {\n      id: 'debts',\n      label: 'Customer Debts',\n      icon: Users,\n      tooltip: 'Customer Debt Management'\n    },\n    {\n      id: 'family-gallery',\n      label: 'Family Gallery',\n      icon: Image,\n      tooltip: 'Family Photos & Memories'\n    },\n  ]\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      // Add to search history\n      const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 5)\n      setSearchHistory(newHistory)\n      localStorage.setItem('searchHistory', JSON.stringify(newHistory))\n\n      // Perform search action\n      console.log('Searching for:', searchQuery)\n      setShowSearchDropdown(false)\n    }\n  }\n\n  const handleSearchFocus = () => {\n    setIsSearchFocused(true)\n    setShowSearchDropdown(true)\n  }\n\n  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value\n    setSearchQuery(value)\n    if (value.length > 0) {\n      setShowSearchDropdown(true)\n    }\n  }\n\n  const selectSearchResult = (result: any) => {\n    setSearchQuery('')\n    setShowSearchDropdown(false)\n    // Navigate to appropriate section based on result type\n    if (result.type === 'product') {\n      setActiveSection('products')\n    } else if (result.type === 'debt') {\n      setActiveSection('debts')\n    }\n  }\n\n  const selectSearchHistory = (historyItem: string) => {\n    setSearchQuery(historyItem)\n    setShowSearchDropdown(false)\n    searchInputRef.current?.focus()\n  }\n\n  const clearSearchHistory = () => {\n    setSearchHistory([])\n    localStorage.removeItem('searchHistory')\n  }\n\n  // Enhanced Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Ctrl/Cmd + K to focus search\n      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {\n        e.preventDefault()\n        searchInputRef.current?.focus()\n        setShowSearchDropdown(true)\n      }\n\n      // Escape to close dropdowns\n      if (e.key === 'Escape') {\n        setShowSearchDropdown(false)\n        setIsSearchFocused(false)\n        setIsNotificationsOpen(false)\n        setIsProfileOpen(false)\n        setIsQuickActionsOpen(false)\n        searchInputRef.current?.blur()\n      }\n\n      // Quick navigation shortcuts (Alt + number)\n      if (e.altKey && !e.ctrlKey && !e.metaKey) {\n        switch (e.key) {\n          case '1':\n            e.preventDefault()\n            setActiveSection('dashboard')\n            break\n          case '2':\n            e.preventDefault()\n            setActiveSection('products')\n            break\n          case '3':\n            e.preventDefault()\n            setActiveSection('debts')\n            break\n          case '4':\n            e.preventDefault()\n            setActiveSection('family-gallery')\n            break\n        }\n      }\n\n      // Quick action shortcuts (Ctrl/Cmd + Shift + letter)\n      if ((e.ctrlKey || e.metaKey) && e.shiftKey) {\n        switch (e.key.toLowerCase()) {\n          case 'p':\n            e.preventDefault()\n            // Quick add product action\n            console.log('Quick add product shortcut')\n            setActiveSection('products')\n            break\n          case 'd':\n            e.preventDefault()\n            // Quick add debt action\n            console.log('Quick add debt shortcut')\n            setActiveSection('debts')\n            break\n          case 'n':\n            e.preventDefault()\n            // Toggle notifications\n            setIsNotificationsOpen(!isNotificationsOpen)\n            break\n          case 't':\n            e.preventDefault()\n            // Toggle theme\n            toggleTheme()\n            break\n          case 'a':\n            e.preventDefault()\n            // Toggle quick actions menu\n            setIsQuickActionsOpen(!isQuickActionsOpen)\n            break\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isNotificationsOpen, isQuickActionsOpen, resolvedTheme, mounted])\n\n  const toggleTheme = () => {\n    if (!mounted) return\n\n    // Manual DOM manipulation for immediate visual feedback\n    const html = document.documentElement\n    const isDark = resolvedTheme === 'dark'\n\n    if (isDark) {\n      html.classList.remove('dark')\n      setTheme('light')\n    } else {\n      html.classList.add('dark')\n      setTheme('dark')\n    }\n  }\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300\" style={{\n      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'\n    }}>\n      <div className=\"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4\">\n        \n        {/* Left Section - Logo & Search (Fixed Width) */}\n        <div className=\"flex items-center space-x-3 w-auto\">\n          {/* Revantad Logo */}\n          <Link\n            href=\"/landing\"\n            className=\"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0\"\n            title=\"Return to Front Page\"\n          >\n            <div className=\"w-10 h-10 hero-gradient rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">R</span>\n            </div>\n            <span className=\"text-xl font-bold text-gradient hidden sm:block\">Revantad</span>\n          </Link>\n\n          {/* Enhanced Search Bar */}\n          <div ref={searchRef} className=\"relative w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48\">\n            <form onSubmit={handleSearch}>\n              <div className=\"relative\">\n                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-200 ${\n                  isSearchFocused ? 'text-green-500' : 'text-gray-400'\n                }`} />\n                <input\n                  ref={searchInputRef}\n                  type=\"text\"\n                  placeholder=\"Search... (Ctrl+K)\"\n                  value={searchQuery}\n                  onChange={handleSearchInputChange}\n                  onFocus={handleSearchFocus}\n                  className={`w-full pl-10 pr-10 py-2 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 ${\n                    isSearchFocused\n                      ? 'ring-2 ring-green-500 bg-white dark:bg-slate-600 shadow-lg'\n                      : 'bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600'\n                  }`}\n                  style={{\n                    backgroundColor: isSearchFocused\n                      ? (resolvedTheme === 'dark' ? '#475569' : '#ffffff')\n                      : (resolvedTheme === 'dark' ? '#334155' : '#f3f4f6')\n                  }}\n                />\n                {searchQuery && (\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setSearchQuery('')\n                      setSearchResults([])\n                      setShowSearchDropdown(false)\n                    }}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </button>\n                )}\n                {!searchQuery && (\n                  <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1\">\n                    <Command className=\"h-3 w-3 text-gray-400\" />\n                    <span className=\"text-xs text-gray-400\">K</span>\n                  </div>\n                )}\n              </div>\n            </form>\n\n            {/* Advanced Search Dropdown */}\n            {showSearchDropdown && (\n              <div\n                className=\"absolute top-full mt-2 w-80 rounded-xl shadow-2xl border z-50 max-h-96 overflow-y-auto\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n                }}\n              >\n                {/* Search Results */}\n                {searchQuery.length >= 2 && (\n                  <div className=\"p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <h4\n                        className=\"text-sm font-semibold transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                        }}\n                      >\n                        Search Results\n                      </h4>\n                      {isSearchLoading && (\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-green-500 border-t-transparent\"></div>\n                      )}\n                    </div>\n\n                    {searchResults.length > 0 ? (\n                      <div className=\"space-y-2\">\n                        {searchResults.map((result, index) => {\n                          const Icon = result.icon\n                          return (\n                            <button\n                              key={index}\n                              onClick={() => selectSearchResult(result)}\n                              className=\"w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                              style={{\n                                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',\n                                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'\n                              }}\n                            >\n                              <div className={`p-2 rounded-full ${\n                                result.type === 'product' ? 'bg-blue-100 dark:bg-blue-900' : 'bg-green-100 dark:bg-green-900'\n                              }`}>\n                                <Icon className={`h-4 w-4 ${\n                                  result.type === 'product' ? 'text-blue-600' : 'text-green-600'\n                                }`} />\n                              </div>\n                              <div className=\"flex-1 text-left\">\n                                <p\n                                  className=\"font-medium text-sm transition-colors duration-300\"\n                                  style={{\n                                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                                  }}\n                                >\n                                  {result.name || result.customer_name}\n                                </p>\n                                <p\n                                  className=\"text-xs transition-colors duration-300\"\n                                  style={{\n                                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                                  }}\n                                >\n                                  {result.type === 'product' ? 'Product' : 'Customer Debt'}\n                                </p>\n                              </div>\n                              <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n                            </button>\n                          )\n                        })}\n                      </div>\n                    ) : !isSearchLoading && (\n                      <div className=\"text-center py-4\">\n                        <p\n                          className=\"text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          No results found\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                )}\n\n                {/* Search History */}\n                {searchQuery.length === 0 && searchHistory.length > 0 && (\n                  <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <h4\n                        className=\"text-sm font-semibold transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                        }}\n                      >\n                        Recent Searches\n                      </h4>\n                      <button\n                        onClick={clearSearchHistory}\n                        className=\"text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200\"\n                      >\n                        Clear\n                      </button>\n                    </div>\n                    <div className=\"space-y-1\">\n                      {searchHistory.map((item, index) => (\n                        <button\n                          key={index}\n                          onClick={() => selectSearchHistory(item)}\n                          className=\"w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                          style={{\n                            backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                          }}\n                        >\n                          <History className=\"h-4 w-4 text-gray-400\" />\n                          <span\n                            className=\"text-sm transition-colors duration-300\"\n                            style={{\n                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                            }}\n                          >\n                            {item}\n                          </span>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Quick Actions */}\n                {searchQuery.length === 0 && (\n                  <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n                    <h4\n                      className=\"text-sm font-semibold mb-3 transition-colors duration-300\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                      }}\n                    >\n                      Quick Actions\n                    </h4>\n                    <div className=\"space-y-1\">\n                      <button\n                        onClick={() => {\n                          setActiveSection('products')\n                          setShowSearchDropdown(false)\n                        }}\n                        className=\"w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                        style={{\n                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                        }}\n                      >\n                        <Package className=\"h-4 w-4 text-blue-500\" />\n                        <span\n                          className=\"text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          View Products\n                        </span>\n                      </button>\n                      <button\n                        onClick={() => {\n                          setActiveSection('debts')\n                          setShowSearchDropdown(false)\n                        }}\n                        className=\"w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                        style={{\n                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                        }}\n                      >\n                        <Users className=\"h-4 w-4 text-green-500\" />\n                        <span\n                          className=\"text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          View Debts\n                        </span>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Center Section - Navigation Icons (Facebook-style) */}\n        <div className=\"hidden sm:flex items-center justify-center\">\n          <div className=\"flex items-center space-x-3 md:space-x-4 lg:space-x-5\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              const isActive = activeSection === item.id\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => setActiveSection(item.id)}\n                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]`}\n                  style={{\n                    backgroundColor: isActive\n                      ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                      : 'transparent',\n                    color: isActive\n                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),\n                    boxShadow: isActive ? '0 2px 8px rgba(34, 197, 94, 0.2)' : 'none'\n                  }}\n                  title={item.tooltip}\n                  onMouseEnter={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(243, 244, 246, 0.8)'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = 'transparent'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'\n                    }\n                  }}\n                >\n                  <Icon className=\"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200\" />\n                  {isActive && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full\"></div>\n                  )}\n\n                  {/* Enhanced Tooltip */}\n                  <div className=\"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10\">\n                    {item.tooltip}\n                    <div className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45\"></div>\n                  </div>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Mobile Navigation - Simplified */}\n        <div className=\"sm:hidden flex items-center justify-center\">\n          <button\n            onClick={() => setActiveSection(activeSection === 'dashboard' ? 'products' : 'dashboard')}\n            className=\"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\"\n            title=\"Toggle View\"\n          >\n            {activeSection === 'dashboard' ? (\n              <Package className=\"h-5 w-5\" />\n            ) : (\n              <Home className=\"h-5 w-5\" />\n            )}\n          </button>\n        </div>\n\n        {/* Right Section - Quick Actions, Notifications, Dark Mode & Profile */}\n        <div className=\"flex items-center justify-end space-x-3\">\n\n          {/* Quick Actions */}\n          <div ref={quickActionsRef} className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsQuickActionsOpen(!isQuickActionsOpen)}\n              className=\"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 group\"\n              title=\"Quick Actions (Ctrl+Shift+A)\"\n            >\n              <Zap className=\"h-5 w-5\" />\n            </button>\n\n            {/* Quick Actions Dropdown */}\n            {isQuickActionsOpen && (\n              <div\n                className=\"absolute right-0 mt-2 w-72 rounded-xl shadow-2xl border z-50\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n                }}\n              >\n                {/* Quick Actions Header */}\n                <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <h3\n                      className=\"text-lg font-semibold transition-colors duration-300\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                      }}\n                    >\n                      Quick Actions\n                    </h3>\n                    <Zap className=\"h-5 w-5 text-yellow-500\" />\n                  </div>\n                  <p\n                    className=\"text-sm mt-1 transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                    }}\n                  >\n                    Keyboard shortcuts for common tasks\n                  </p>\n                </div>\n\n                {/* Quick Actions List */}\n                <div className=\"p-2 space-y-1\">\n                  <button\n                    onClick={() => {\n                      setActiveSection('products')\n                      setIsQuickActionsOpen(false)\n                    }}\n                    className=\"w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"p-2 rounded-full bg-blue-100 dark:bg-blue-900\">\n                        <Package className=\"h-4 w-4 text-blue-600\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p\n                          className=\"font-medium text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                          }}\n                        >\n                          Add Product\n                        </p>\n                        <p\n                          className=\"text-xs transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          Add new inventory item\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n                      <span>Ctrl+Shift+P</span>\n                    </div>\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setActiveSection('debts')\n                      setIsQuickActionsOpen(false)\n                    }}\n                    className=\"w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"p-2 rounded-full bg-green-100 dark:bg-green-900\">\n                        <Users className=\"h-4 w-4 text-green-600\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p\n                          className=\"font-medium text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                          }}\n                        >\n                          Record Debt\n                        </p>\n                        <p\n                          className=\"text-xs transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          Add customer debt record\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n                      <span>Ctrl+Shift+D</span>\n                    </div>\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setIsNotificationsOpen(true)\n                      setIsQuickActionsOpen(false)\n                    }}\n                    className=\"w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"p-2 rounded-full bg-purple-100 dark:bg-purple-900\">\n                        <Bell className=\"h-4 w-4 text-purple-600\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p\n                          className=\"font-medium text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                          }}\n                        >\n                          Notifications\n                        </p>\n                        <p\n                          className=\"text-xs transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          View notifications\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n                      <span>Ctrl+Shift+N</span>\n                    </div>\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      toggleTheme()\n                      setIsQuickActionsOpen(false)\n                    }}\n                    className=\"w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"p-2 rounded-full bg-yellow-100 dark:bg-yellow-900\">\n                        {resolvedTheme === 'dark' ? (\n                          <Sun className=\"h-4 w-4 text-yellow-600\" />\n                        ) : (\n                          <Moon className=\"h-4 w-4 text-yellow-600\" />\n                        )}\n                      </div>\n                      <div className=\"text-left\">\n                        <p\n                          className=\"font-medium text-sm transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                          }}\n                        >\n                          Toggle Theme\n                        </p>\n                        <p\n                          className=\"text-xs transition-colors duration-300\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          Switch to {resolvedTheme === 'dark' ? 'light' : 'dark'} mode\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n                      <span>Ctrl+Shift+T</span>\n                    </div>\n                  </button>\n                </div>\n\n                {/* Navigation Shortcuts */}\n                <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n                  <h4\n                    className=\"text-sm font-semibold mb-3 transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                    }}\n                  >\n                    Navigation Shortcuts\n                  </h4>\n                  <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                    <div className=\"flex justify-between\">\n                      <span\n                        className=\"transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Dashboard\n                      </span>\n                      <span className=\"text-gray-400\">Alt+1</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span\n                        className=\"transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Products\n                      </span>\n                      <span className=\"text-gray-400\">Alt+2</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span\n                        className=\"transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Debts\n                      </span>\n                      <span className=\"text-gray-400\">Alt+3</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span\n                        className=\"transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Gallery\n                      </span>\n                      <span className=\"text-gray-400\">Alt+4</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Notifications */}\n          <div ref={notificationRef} className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n              className=\"relative p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 group\"\n              title=\"Notifications\"\n            >\n              <Bell className=\"h-5 w-5\" />\n              {unreadCount > 0 && (\n                <>\n                  <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center animate-pulse\">\n                    <span className=\"text-xs font-bold text-white\">\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </span>\n                  </div>\n                  <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full animate-ping opacity-75\"></div>\n                </>\n              )}\n            </button>\n\n            {/* Notifications Dropdown */}\n            {isNotificationsOpen && (\n              <div\n                className=\"absolute right-0 mt-2 w-80 rounded-xl shadow-2xl border z-50 max-h-96 overflow-hidden\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n                }}\n              >\n                {/* Notifications Header */}\n                <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <h3\n                      className=\"text-lg font-semibold transition-colors duration-300\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                      }}\n                    >\n                      Notifications\n                    </h3>\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={toggleSound}\n                        className=\"p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200\"\n                        title={soundEnabled ? 'Disable sound' : 'Enable sound'}\n                      >\n                        {soundEnabled ? (\n                          <Volume2 className=\"h-4 w-4 text-green-500\" />\n                        ) : (\n                          <VolumeX className=\"h-4 w-4 text-gray-400\" />\n                        )}\n                      </button>\n                      {unreadCount > 0 && (\n                        <button\n                          onClick={markAllAsRead}\n                          className=\"text-xs text-blue-500 hover:text-blue-700 transition-colors duration-200\"\n                        >\n                          Mark all read\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                  {unreadCount > 0 && (\n                    <p\n                      className=\"text-sm mt-1 transition-colors duration-300\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                      }}\n                    >\n                      {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n                    </p>\n                  )}\n                </div>\n\n                {/* Notifications List */}\n                <div className=\"max-h-80 overflow-y-auto\">\n                  {notifications.length > 0 ? (\n                    <div className=\"space-y-1 p-2\">\n                      {notifications.map((notification) => {\n                        const Icon = notification.icon\n                        return (\n                          <button\n                            key={notification.id}\n                            onClick={() => markAsRead(notification.id)}\n                            className={`w-full p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] text-left ${\n                              !notification.read ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''\n                            }`}\n                            style={{\n                              backgroundColor: notification.read\n                                ? (resolvedTheme === 'dark' ? '#334155' : '#f8fafc')\n                                : undefined\n                            }}\n                          >\n                            <div className=\"flex items-start space-x-3\">\n                              <div className={`p-2 rounded-full flex-shrink-0 ${\n                                notification.color === 'orange' ? 'bg-orange-100 dark:bg-orange-900' :\n                                notification.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900' :\n                                notification.color === 'green' ? 'bg-green-100 dark:bg-green-900' :\n                                'bg-gray-100 dark:bg-gray-900'\n                              }`}>\n                                <Icon className={`h-4 w-4 ${\n                                  notification.color === 'orange' ? 'text-orange-600' :\n                                  notification.color === 'blue' ? 'text-blue-600' :\n                                  notification.color === 'green' ? 'text-green-600' :\n                                  'text-gray-600'\n                                }`} />\n                              </div>\n                              <div className=\"flex-1 min-w-0\">\n                                <div className=\"flex items-center justify-between\">\n                                  <p\n                                    className=\"font-medium text-sm transition-colors duration-300\"\n                                    style={{\n                                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                                    }}\n                                  >\n                                    {notification.title}\n                                  </p>\n                                  {!notification.read && (\n                                    <div className=\"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\"></div>\n                                  )}\n                                </div>\n                                <p\n                                  className=\"text-xs mt-1 transition-colors duration-300\"\n                                  style={{\n                                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                                  }}\n                                >\n                                  {notification.message}\n                                </p>\n                                <p\n                                  className=\"text-xs mt-1 transition-colors duration-300\"\n                                  style={{\n                                    color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'\n                                  }}\n                                >\n                                  {notification.timestamp.toLocaleTimeString()}\n                                </p>\n                              </div>\n                            </div>\n                          </button>\n                        )\n                      })}\n                    </div>\n                  ) : (\n                    <div className=\"p-8 text-center\">\n                      <Bell className=\"h-12 w-12 text-gray-400 mx-auto mb-3\" />\n                      <p\n                        className=\"text-sm transition-colors duration-300\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        No notifications yet\n                      </p>\n                    </div>\n                  )}\n                </div>\n\n                {/* Notifications Footer */}\n                {notifications.length > 0 && (\n                  <div className=\"p-3 border-t border-gray-200 dark:border-gray-700\">\n                    <button\n                      onClick={() => {\n                        setIsNotificationsOpen(false)\n                        // Navigate to notifications page or history\n                      }}\n                      className=\"w-full text-center py-2 text-sm text-blue-500 hover:text-blue-700 transition-colors duration-200\"\n                    >\n                      View all notifications\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n\n          {/* Dark Mode Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0\"\n            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}\n            disabled={!mounted}\n          >\n            {!mounted ? (\n              <div className=\"h-5 w-5 bg-gray-400 rounded-full animate-pulse\" />\n            ) : resolvedTheme === 'dark' ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group\"\n            >\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow\">\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors\">\n                {user?.name || 'Admin'}\n              </span>\n            </button>\n\n            {/* Dropdown Menu */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1\">\n                <div className=\"px-4 py-2 border-b border-gray-200 dark:border-slate-700\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user?.name || 'Admin User'}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.email || '<EMAIL>'}</p>\n                </div>\n                \n                <button\n                  onClick={() => setActiveSection('settings')}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>Settings</span>\n                </button>\n                \n                <button\n                  onClick={handleLogout}\n                  className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Logout</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AACA;;;AA9BA;;;;;;AAqCe,SAAS,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAoB;;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE/C,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YACX,wCAAwC;YACxC,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,iBAAiB,KAAK,KAAK,CAAC;YAC9B;YAEA,gCAAgC;YAChC,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,cAAc,MAAM;gBACtB,gBAAgB,KAAK,KAAK,CAAC;YAC7B;YAEA,2BAA2B;YAC3B;QACF;gCAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,qBAAqB;QACzB,IAAI;YACF,kCAAkC;YAClC,MAAM,oBAAoB;gBACxB;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;oBACN,MAAM,2NAAA,CAAA,gBAAa;oBACnB,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;oBACN,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;oBAC3C,MAAM;oBACN,MAAM,8NAAA,CAAA,cAAW;oBACjB,OAAO;gBACT;aACD;YAED,iBAAiB;YACjB,eAAe,kBAAkB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB;QAC5B,IAAI,gBAAgB,SAAS;YAC3B,qCAAqC;YACrC,MAAM,eAAe,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;YACnF,MAAM,aAAa,aAAa,gBAAgB;YAChD,MAAM,WAAW,aAAa,UAAU;YAExC,WAAW,OAAO,CAAC;YACnB,SAAS,OAAO,CAAC,aAAa,WAAW;YAEzC,WAAW,SAAS,CAAC,cAAc,CAAC,KAAK,aAAa,WAAW;YACjE,WAAW,SAAS,CAAC,cAAc,CAAC,KAAK,aAAa,WAAW,GAAG;YAEpE,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,aAAa,WAAW;YAC1D,SAAS,IAAI,CAAC,4BAA4B,CAAC,MAAM,aAAa,WAAW,GAAG;YAE5E,WAAW,KAAK,CAAC,aAAa,WAAW;YACzC,WAAW,IAAI,CAAC,aAAa,WAAW,GAAG;QAC7C;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,IAAI;QAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAC5C;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,CAAC;QAC5D,eAAe;IACjB;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,MAAM,kBAAkB,CAAC;QACzB,gBAAgB;QAChB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E,sBAAsB;wBACtB,mBAAmB;oBACrB;oBACA,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACtF,uBAAuB;oBACzB;oBACA,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACtF,sBAAsB;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;gCAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW;kDAAY;oBAC3B,8DAA8D;oBAC9D,IAAI,KAAK,MAAM,KAAK,KAAK;wBACvB,MAAM,kBAAkB;4BACtB,IAAI,KAAK,GAAG;4BACZ,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,WAAW,IAAI;4BACf,MAAM;4BACN,MAAM,qMAAA,CAAA,OAAI;4BACV,OAAO;wBACT;wBAEA;8DAAiB,CAAA,OAAQ;oCAAC;uCAAoB,KAAK,KAAK,CAAC,GAAG;iCAAG;6DAAE,6BAA6B;;wBAC9F;8DAAe,CAAA,OAAQ,OAAO;;wBAC9B;oBACF;gBACF;iDAAG,OAAO,yBAAyB;;YAEnC;yCAAO,IAAM,cAAc;;QAC7B;gCAAG;QAAC;QAAc;KAAQ;IAE1B,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;uDAAgB;oBACpB,IAAI,YAAY,MAAM,GAAG,GAAG;wBAC1B,iBAAiB,EAAE;wBACnB;oBACF;oBAEA,mBAAmB;oBACnB,IAAI;wBACF,gCAAgC;wBAChC,MAAM,CAAC,aAAa,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAChD,MAAM,CAAC,qBAAqB,EAAE,mBAAmB,cAAc;4BAC/D,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,cAAc;yBAC7D;wBAED,MAAM,eAAe,MAAM,YAAY,IAAI;wBAC3C,MAAM,YAAY,MAAM,SAAS,IAAI;wBAErC,MAAM,UAAU;+BACV,CAAC,aAAa,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG;uEAAC,CAAC,OAAc,CAAC;wCAChE,GAAG,IAAI;wCACP,MAAM;wCACN,MAAM,2MAAA,CAAA,UAAO;oCACf,CAAC;;+BACG,CAAC,UAAU,KAAK,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG;uEAAC,CAAC,OAAc,CAAC;wCAC1D,GAAG,IAAI;wCACP,MAAM;wCACN,MAAM,uMAAA,CAAA,QAAK;oCACb,CAAC;;yBACF;wBAED,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,iBAAiB,EAAE;oBACrB,SAAU;wBACR,mBAAmB;oBACrB;gBACF;;YAEA,MAAM,gBAAgB,WAAW,eAAe;YAChD;yCAAO,IAAM,aAAa;;QAC5B;gCAAG;QAAC;KAAY;IAIhB,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,OAAI;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,wBAAwB;YACxB,MAAM,aAAa;gBAAC;mBAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;aAAa,CAAC,KAAK,CAAC,GAAG;YAC3F,iBAAiB;YACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,wBAAwB;YACxB,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,sBAAsB;QACxB;IACF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,sBAAsB;IACxB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,sBAAsB;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf,sBAAsB;QACtB,uDAAuD;QACvD,IAAI,OAAO,IAAI,KAAK,WAAW;YAC7B,iBAAiB;QACnB,OAAO,IAAI,OAAO,IAAI,KAAK,QAAQ;YACjC,iBAAiB;QACnB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe;QACf,sBAAsB;QACtB,eAAe,OAAO,EAAE;IAC1B;IAEA,MAAM,qBAAqB;QACzB,iBAAiB,EAAE;QACnB,aAAa,UAAU,CAAC;IAC1B;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;uDAAgB,CAAC;oBACrB,+BAA+B;oBAC/B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,eAAe,OAAO,EAAE;wBACxB,sBAAsB;oBACxB;oBAEA,4BAA4B;oBAC5B,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,sBAAsB;wBACtB,mBAAmB;wBACnB,uBAAuB;wBACvB,iBAAiB;wBACjB,sBAAsB;wBACtB,eAAe,OAAO,EAAE;oBAC1B;oBAEA,4CAA4C;oBAC5C,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE;wBACxC,OAAQ,EAAE,GAAG;4BACX,KAAK;gCACH,EAAE,cAAc;gCAChB,iBAAiB;gCACjB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,iBAAiB;gCACjB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,iBAAiB;gCACjB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,iBAAiB;gCACjB;wBACJ;oBACF;oBAEA,qDAAqD;oBACrD,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,QAAQ,EAAE;wBAC1C,OAAQ,EAAE,GAAG,CAAC,WAAW;4BACvB,KAAK;gCACH,EAAE,cAAc;gCAChB,2BAA2B;gCAC3B,QAAQ,GAAG,CAAC;gCACZ,iBAAiB;gCACjB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,wBAAwB;gCACxB,QAAQ,GAAG,CAAC;gCACZ,iBAAiB;gCACjB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,uBAAuB;gCACvB,uBAAuB,CAAC;gCACxB;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,eAAe;gCACf;gCACA;4BACF,KAAK;gCACH,EAAE,cAAc;gCAChB,4BAA4B;gCAC5B,sBAAsB,CAAC;gCACvB;wBACJ;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;gCAAG;QAAC;QAAqB;QAAoB;QAAe;KAAQ;IAEpE,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QAEd,wDAAwD;QACxD,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,SAAS,kBAAkB;QAEjC,IAAI,QAAQ;YACV,KAAK,SAAS,CAAC,MAAM,CAAC;YACtB,SAAS;QACX,OAAO;YACL,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;QAAgJ,OAAO;YACvK,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;QACtD;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAM;;8CAEN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAIpE,6LAAC;4BAAI,KAAK;4BAAW,WAAU;;8CAC7B,6LAAC;oCAAK,UAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,0FAA0F,EAC5G,kBAAkB,mBAAmB,iBACrC;;;;;;0DACF,6LAAC;gDACC,KAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,WAAW,CAAC,iIAAiI,EAC3I,kBACI,+DACA,2EACJ;gDACF,OAAO;oDACL,iBAAiB,kBACZ,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;gDAC9C;;;;;;4CAED,6BACC,6LAAC;gDACC,MAAK;gDACL,SAAS;oDACP,eAAe;oDACf,iBAAiB,EAAE;oDACnB,sBAAsB;gDACxB;gDACA,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;4CAGhB,CAAC,6BACA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;gCAO/C,oCACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;oCACtD;;wCAGC,YAAY,MAAM,IAAI,mBACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;sEACD;;;;;;wDAGA,iCACC,6LAAC;4DAAI,WAAU;;;;;;;;;;;;gDAIlB,cAAc,MAAM,GAAG,kBACtB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,QAAQ;wDAC1B,MAAM,OAAO,OAAO,IAAI;wDACxB,qBACE,6LAAC;4DAEC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;gEACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4DAC3D;;8EAEA,6LAAC;oEAAI,WAAW,CAAC,iBAAiB,EAChC,OAAO,IAAI,KAAK,YAAY,iCAAiC,kCAC7D;8EACA,cAAA,6LAAC;wEAAK,WAAW,CAAC,QAAQ,EACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,kBAC9C;;;;;;;;;;;8EAEJ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFAEC,OAAO,IAAI,IAAI,OAAO,aAAa;;;;;;sFAEtC,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFAEC,OAAO,IAAI,KAAK,YAAY,YAAY;;;;;;;;;;;;8EAG7C,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;2DAjCjB;;;;;oDAoCX;;;;;2DAEA,CAAC,iCACH,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;kEACD;;;;;;;;;;;;;;;;;wCASR,YAAY,MAAM,KAAK,KAAK,cAAc,MAAM,GAAG,mBAClD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;sEACD;;;;;;sEAGD,6LAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;8DAIH,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;4DAEC,SAAS,IAAM,oBAAoB;4DACnC,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;4DAC1D;;8EAEA,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EAEC;;;;;;;2DAdE;;;;;;;;;;;;;;;;wCAuBd,YAAY,MAAM,KAAK,mBACtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;oDAChD;8DACD;;;;;;8DAGD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS;gEACP,iBAAiB;gEACjB,sBAAsB;4DACxB;4DACA,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;4DAC1D;;8EAEA,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;;;;;;;sEAIH,6LAAC;4DACC,SAAS;gEACP,iBAAiB;gEACjB,sBAAsB;4DACxB;4DACA,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;4DAC1D;;8EAEA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAajB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;4BAE1C,qBACE,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gCACvC,WAAW,CAAC,yIAAyI,CAAC;gCACtJ,OAAO;oCACL,iBAAiB,WACZ,kBAAkB,SAAS,2BAA2B,2BACvD;oCACJ,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oCAC5C,WAAW,WAAW,qCAAqC;gCAC7D;gCACA,OAAO,KAAK,OAAO;gCACnB,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,2BAA2B;wCAC9F,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;gCACA,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;;kDAEA,6LAAC;wCAAK,WAAU;;;;;;oCACf,0BACC,6LAAC;wCAAI,WAAU;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO;0DACb,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAlCZ,KAAK,EAAE;;;;;wBAsClB;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS,IAAM,iBAAiB,kBAAkB,cAAc,aAAa;wBAC7E,WAAU;wBACV,OAAM;kCAEL,kBAAkB,4BACjB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAMtB,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,KAAK;4BAAiB,WAAU;;8CACnC,6LAAC;oCACC,SAAS,IAAM,sBAAsB,CAAC;oCACtC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;gCAIhB,oCACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;oCACtD;;sDAGA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;sEACD;;;;;;sEAGD,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;oDAChD;8DACD;;;;;;;;;;;;sDAMH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;wDACP,iBAAiB;wDACjB,sBAAsB;oDACxB;oDACA,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDAC1D;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;8EAErB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;sFAGD,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;;;;;;;;;;;;;sEAKL,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC;oDACC,SAAS;wDACP,iBAAiB;wDACjB,sBAAsB;oDACxB;oDACA,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDAC1D;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;sFAGD,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;;;;;;;;;;;;;sEAKL,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC;oDACC,SAAS;wDACP,uBAAuB;wDACvB,sBAAsB;oDACxB;oDACA,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDAC1D;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;sFAGD,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;;;;;;;;;;;;;sEAKL,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC;oDACC,SAAS;wDACP;wDACA,sBAAsB;oDACxB;oDACA,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDAC1D;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,uBACjB,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;6FAEf,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFACD;;;;;;sFAGD,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;;gFACD;gFACY,kBAAkB,SAAS,UAAU;gFAAO;;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;sDAMZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;oDAChD;8DACD;;;;;;8DAGD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;8EAGD,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;8EAGD,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;8EAGD,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EACD;;;;;;8EAGD,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5C,6LAAC;4BAAI,KAAK;4BAAiB,WAAU;;8CACnC,6LAAC;oCACC,SAAS,IAAM,uBAAuB,CAAC;oCACvC,WAAU;oCACV,OAAM;;sDAEN,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb;;8DACE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,cAAc,IAAI,OAAO;;;;;;;;;;;8DAG9B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;gCAMpB,qCACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;oCACtD;;sDAGA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;sEACD;;;;;;sEAGD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,SAAS;oEACT,WAAU;oEACV,OAAO,eAAe,kBAAkB;8EAEvC,6BACC,6LAAC,+MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAEnB,6LAAC,+MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;gEAGtB,cAAc,mBACb,6LAAC;oEACC,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;gDAMN,cAAc,mBACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;wDAEC;wDAAY;wDAAqB,gBAAgB,IAAI,MAAM;;;;;;;;;;;;;sDAMlE,6LAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;gDAAI,WAAU;0DACZ,cAAc,GAAG,CAAC,CAAC;oDAClB,MAAM,OAAO,aAAa,IAAI;oDAC9B,qBACE,6LAAC;wDAEC,SAAS,IAAM,WAAW,aAAa,EAAE;wDACzC,WAAW,CAAC,+EAA+E,EACzF,CAAC,aAAa,IAAI,GAAG,+EAA+E,IACpG;wDACF,OAAO;4DACL,iBAAiB,aAAa,IAAI,GAC7B,kBAAkB,SAAS,YAAY,YACxC;wDACN;kEAEA,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,+BAA+B,EAC9C,aAAa,KAAK,KAAK,WAAW,qCAClC,aAAa,KAAK,KAAK,SAAS,iCAChC,aAAa,KAAK,KAAK,UAAU,mCACjC,gCACA;8EACA,cAAA,6LAAC;wEAAK,WAAW,CAAC,QAAQ,EACxB,aAAa,KAAK,KAAK,WAAW,oBAClC,aAAa,KAAK,KAAK,SAAS,kBAChC,aAAa,KAAK,KAAK,UAAU,mBACjC,iBACA;;;;;;;;;;;8EAEJ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFACC,WAAU;oFACV,OAAO;wFACL,OAAO,kBAAkB,SAAS,YAAY;oFAChD;8FAEC,aAAa,KAAK;;;;;;gFAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC;oFAAI,WAAU;;;;;;;;;;;;sFAGnB,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFAEC,aAAa,OAAO;;;;;;sFAEvB,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFAEC,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;uDArD3C,aAAa,EAAE;;;;;gDA2D1B;;;;;qEAGF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;kEACD;;;;;;;;;;;;;;;;;wCAQN,cAAc,MAAM,GAAG,mBACtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS;oDACP,uBAAuB;gDACvB,4CAA4C;gDAC9C;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAWX,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO,UAAU,CAAC,UAAU,EAAE,kBAAkB,SAAS,UAAU,OAAO,gBAAgB,EAAE,cAAc,CAAC,CAAC,GAAG;4BAC/G,UAAU,CAAC;sCAEV,CAAC,wBACA,6LAAC;gCAAI,WAAU;;;;;uCACb,kBAAkB,uBACpB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;qDAEf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDACb,MAAM,QAAQ;;;;;;;;;;;;gCAKlB,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqD,MAAM,QAAQ;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAA4C,MAAM,SAAS;;;;;;;;;;;;sDAG1E,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GApsCwB;;QAOc,mJAAA,CAAA,WAAQ;QAQnB,kIAAA,CAAA,UAAO;;;KAfV", "debugId": null}}]}