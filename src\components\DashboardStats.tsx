'use client'

import { useState, useEffect } from 'react'
import {
  Package,
  Users,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Calendar,
  BarChart3,
  PieChart,
  RefreshCw,
  Eye,
  ShoppingCart,
  CreditCard,
  Zap
} from 'lucide-react'
import { useTheme } from 'next-themes'

import type { DashboardStats } from '@/types'

interface DashboardStatsProps {
  stats: DashboardStats
}

export default function DashboardStats({ stats }: DashboardStatsProps) {
  const { resolvedTheme } = useTheme()
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [animateStats, setAnimateStats] = useState(false)

  // Simulate real-time updates
  useEffect(() => {
    setAnimateStats(true)
    const timer = setTimeout(() => setAnimateStats(false), 1000)
    return () => clearTimeout(timer)
  }, [stats])

  // Auto-refresh functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date())
    }, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setLastUpdated(new Date())
    }, 1000)
  }

  const statCards = [
    {
      title: 'Products in List',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: '+12%',
      trendUp: true,
      description: 'Total products available',
      actionIcon: Eye
    },
    {
      title: 'Customer Debts',
      value: stats.totalDebts,
      icon: Users,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: '+5%',
      trendUp: true,
      description: 'Active debt records',
      actionIcon: CreditCard
    },
    {
      title: 'Total Debt Amount',
      value: `₱${stats.totalDebtAmount.toFixed(2)}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      trend: '-8%',
      trendUp: false,
      description: 'Outstanding payments',
      actionIcon: TrendingDown
    },
    {
      title: 'Low Stock Items',
      value: stats.lowStockItems,
      icon: AlertTriangle,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50',
      trend: stats.lowStockItems > 5 ? '+15%' : '-3%',
      trendUp: stats.lowStockItems <= 5,
      description: 'Need restocking',
      actionIcon: ShoppingCart
    },
  ]

  return (
    <div className="space-y-6">
      {/* Enhanced Dashboard Header */}
      <div
        className="rounded-xl shadow-lg p-6 transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
        }}
      >
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1
              className="text-3xl font-bold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              Dashboard Overview
            </h1>
            <p
              className="text-lg mt-1 transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Overview of your Revantad Store
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Last updated
              </p>
              <p
                className="text-sm font-medium transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {lastUpdated.toLocaleTimeString()}
              </p>
            </div>
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-3 rounded-xl transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f3f4f6',
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'
              }}
              title="Refresh Dashboard"
            >
              <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {/* Live Status Indicators */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              System Online
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-blue-500" />
            <span
              className="text-sm font-medium transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Real-time Updates
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-purple-500" />
            <span
              className="text-sm font-medium transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Auto-refresh: 30s
            </span>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          const ActionIcon = card.actionIcon
          const TrendIcon = card.trendUp ? TrendingUp : TrendingDown

          return (
            <div
              key={index}
              className={`rounded-xl shadow-lg p-6 transition-all duration-500 hover:shadow-xl hover:scale-[1.02] cursor-pointer group ${
                animateStats ? 'animate-pulse' : ''
              }`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
                transform: animateStats ? 'translateY(-2px)' : 'translateY(0)',
              }}
            >
              {/* Card Header */}
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl ${card.bgColor} ${resolvedTheme === 'dark' ? 'opacity-90' : ''} group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`h-6 w-6 ${card.textColor}`} />
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    card.trendUp
                      ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                      : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                  }`}>
                    <TrendIcon className="h-3 w-3" />
                    <span>{card.trend}</span>
                  </div>
                </div>
              </div>

              {/* Card Content */}
              <div className="space-y-2">
                <p
                  className="text-sm font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  {card.title}
                </p>
                <p
                  className="text-3xl font-bold transition-colors duration-300 group-hover:scale-105 transform"
                  style={{
                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                  }}
                >
                  {card.value}
                </p>
                <p
                  className="text-xs transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'
                  }}
                >
                  {card.description}
                </p>
              </div>

              {/* Card Action */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  className="flex items-center space-x-2 text-sm font-medium transition-all duration-200 hover:scale-105"
                  style={{
                    color: resolvedTheme === 'dark' ? '#60a5fa' : '#3b82f6'
                  }}
                >
                  <ActionIcon className="h-4 w-4" />
                  <span>View Details</span>
                </button>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          )
        })}
      </div>

      {/* Enhanced Quick Actions */}
      <div
        className="rounded-xl shadow-lg p-6 transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
        }}
      >
        <div className="flex items-center justify-between mb-6">
          <h3
            className="text-xl font-bold transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
            }}
          >
            Quick Actions
          </h3>
          <Zap className="h-5 w-5 text-yellow-500" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Add Product Action */}
          <button
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg relative overflow-hidden"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative z-10 text-center">
              <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900 group-hover:scale-110 transition-transform duration-300 mb-3">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <p
                className="font-semibold mb-1 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Add Product
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                New inventory item
              </p>
            </div>
          </button>

          {/* Record Debt Action */}
          <button
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg relative overflow-hidden"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative z-10 text-center">
              <div className="p-4 rounded-full bg-green-100 dark:bg-green-900 group-hover:scale-110 transition-transform duration-300 mb-3">
                <CreditCard className="h-8 w-8 text-green-600" />
              </div>
              <p
                className="font-semibold mb-1 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Record Debt
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Customer payment
              </p>
            </div>
          </button>

          {/* View Analytics Action */}
          <button
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg relative overflow-hidden"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative z-10 text-center">
              <div className="p-4 rounded-full bg-purple-100 dark:bg-purple-900 group-hover:scale-110 transition-transform duration-300 mb-3">
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
              <p
                className="font-semibold mb-1 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Analytics
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Business insights
              </p>
            </div>
          </button>

          {/* Generate Report Action */}
          <button
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg relative overflow-hidden"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative z-10 text-center">
              <div className="p-4 rounded-full bg-orange-100 dark:bg-orange-900 group-hover:scale-110 transition-transform duration-300 mb-3">
                <PieChart className="h-8 w-8 text-orange-600" />
              </div>
              <p
                className="font-semibold mb-1 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Reports
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Generate reports
              </p>
            </div>
          </button>
        </div>
      </div>

      {/* Enhanced Store Overview & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Store Overview */}
        <div
          className="rounded-xl shadow-lg p-6 transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3
              className="text-xl font-bold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              Store Overview
            </h3>
            <BarChart3 className="h-5 w-5 text-blue-500" />
          </div>

          <div className="space-y-4">
            <div
              className="flex justify-between items-center py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02]"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center space-x-3">
                <Package className="h-5 w-5 text-blue-500" />
                <span
                  className="font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  Products in List
                </span>
              </div>
              <span
                className="text-xl font-bold transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {stats.totalProducts}
              </span>
            </div>

            <div
              className="flex justify-between items-center py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02]"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center space-x-3">
                <Users className="h-5 w-5 text-green-500" />
                <span
                  className="font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  Outstanding Debts
                </span>
              </div>
              <span
                className="text-xl font-bold transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {stats.totalDebts}
              </span>
            </div>

            <div
              className="flex justify-between items-center py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02]"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center space-x-3">
                <DollarSign className="h-5 w-5 text-yellow-500" />
                <span
                  className="font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  Total Amount Owed
                </span>
              </div>
              <span
                className="text-xl font-bold transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                ₱{stats.totalDebtAmount.toFixed(2)}
              </span>
            </div>

            <div
              className="flex justify-between items-center py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02]"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <span
                  className="font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  Items Need Restocking
                </span>
              </div>
              <span className={`text-xl font-bold ${stats.lowStockItems > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.lowStockItems}
              </span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div
          className="rounded-xl shadow-lg p-6 transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3
              className="text-xl font-bold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              Recent Activity
            </h3>
            <Activity className="h-5 w-5 text-purple-500" />
          </div>

          <div className="space-y-4">
            {/* Recent Products */}
            {stats.recentProducts.slice(0, 3).map((product, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.02]"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                  border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
                }}
              >
                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                  <Package className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p
                    className="font-medium text-sm transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {product.name}
                  </p>
                  <p
                    className="text-xs transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}
                  >
                    Added to inventory
                  </p>
                </div>
                <span
                  className="text-xs font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'
                  }}
                >
                  2h ago
                </span>
              </div>
            ))}

            {/* Recent Debts */}
            {stats.recentDebts.slice(0, 2).map((debt, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.02]"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f8fafc',
                  border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
                }}
              >
                <div className="p-2 rounded-full bg-green-100 dark:bg-green-900">
                  <CreditCard className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <p
                    className="font-medium text-sm transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {debt.customer_name}
                  </p>
                  <p
                    className="text-xs transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}
                  >
                    New debt record
                  </p>
                </div>
                <span
                  className="text-xs font-medium transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'
                  }}
                >
                  1h ago
                </span>
              </div>
            ))}

            {/* View All Activity Button */}
            <button
              className="w-full mt-4 py-3 px-4 rounded-lg transition-all duration-300 hover:scale-[1.02] border-2 border-dashed"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              <div className="flex items-center justify-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span className="font-medium">View All Activity</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
